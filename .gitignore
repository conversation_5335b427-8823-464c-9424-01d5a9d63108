debug/
release/
ui_*
Makefile*
object_script*
*.pro.user
icu*.dll
Qt5*.dll
sanguosha.qm
config.ini
swig/sanguosha_wrap.cxx
records/*
users.db
doc/*.html
swig/*.bat
.DS_Store
liblua5.1.dylib
font/font.ttf
QSanguosha.pro.user*
etc/customScenes/custom_scenario.txt
#*.dylib
QSanguosha.exe
QSanguosha
QSanguosha.app
debugQSanguosha
lib/jsoncpp.lib
lib/lua.lib
lib/breakpad.lib
/tools/
builds/vs2010/Win32/
Bin/
/builds/vs2010/ipch
/lib/lua.lib
/lib/jsoncpp.lib
/lib/breakpad.lib
/builds/vs2013/ipch
/builds/vs2010/ipch
/builds/vs2010/QSanguosha.sdf
/builds/vs2010/QSanguosha.suo
/builds/vs2010/*.user
/builds/vs2010/*.opensdf
/QSanguosha.vcxproj
/QSanguosha.vcxproj.filters
tags
/extensions
*~
*.kdev4
.cproject
.project
.settings
/lua/sgs_ex2.lua
/image/system/button/promptbox
/image/system/button/back2.png
/image/system/button/back.png
/image/system/button/back_shade.png
/extension-doc
/extensions
card_intention.txt
ai.html
*.sh
/src/lua/print.c
/src/lua/luac.c
/image/system/ark.png
/image/system/big-back.png
/image/system/settings.png
/image/system/skill-dock.png
/image/system/small-back.png
/image/system/to.png
/src/dialog/halldialog.*
/QtCore4.dll
/QtDeclarative4.dll
/QtGui4.dll
/QtNetwork4.dll
/QtScript4.dll
/QtSql4.dll
/QtXmlPatterns4.dll
dmp/*.dmp
*.opensdf
*.sdf
*.suo
*.vcxproj.user
OGLdpf.log
imageformats/
msvc*.dll
platforms/
*.orig
libEGL.dll
libGLESv2.dll
fmod.log
*.dll
src/bot_version.cpp
*.lastcodeanalysissucceeded
*.ipch
serverlist.log
record/