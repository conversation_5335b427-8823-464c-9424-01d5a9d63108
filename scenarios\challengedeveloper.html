﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN"
    "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
<meta http-equiv="Content-Type" content="application/xhtml+xml; charset=UTF-8" />
<meta name="generator" content="AsciiDoc 8.6.6" />
<title>挑战开发组</title>
<style type="text/css">
/* Sans-serif font. */
h1, h2, h3, h4, h5, h6,
div.title, caption.title,
thead, p.table.header,
div#toctitle,
span#author, span#revnumber, span#revdate, span#revremark,
div#footer {
  font-family: Arial,Helvetica,sans-serif;
}

/* Serif font. */
div.sectionbody {
  font-family: Georgia,"Times New Roman",Times,serif;
}

/* Monospace font. */
tt {
  font-size: inherit;
}

body {
  margin: 1em 5% 1em 5%;
}

a {
  color: blue;
  text-decoration: underline;
}
a:visited {
  color: fuchsia;
}

em {
  font-style: italic;
  color: navy;
}

strong {
  font-weight: bold;
  color: #083194;
}

tt {
  font-size: inherit;
  color: navy;
}

h1, h2, h3, h4, h5, h6 {
  color: #527bbd;
  margin-top: 1.2em;
  margin-bottom: 0.5em;
  line-height: 1.3;
}

h1, h2, h3 {
  border-bottom: 2px solid silver;
}
h2 {
  padding-top: 0.5em;
}
h3 {
  float: left;
}
h3 + * {
  clear: left;
}

div.sectionbody {
  margin-left: 0;
}

hr {
  border: 1px solid silver;
}

p {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

ul, ol, li > p {
  margin-top: 0;
}
ul > li     { color: #aaa; }
ul > li > * { color: black; }

pre {
  padding: 0;
  margin: 0;
}

span#author {
  color: #527bbd;
  font-weight: bold;
  font-size: 1.1em;
}
span#email {
}
span#revnumber, span#revdate, span#revremark {
}

div#footer {
  font-size: small;
  border-top: 2px solid silver;
  padding-top: 0.5em;
  margin-top: 4.0em;
}
div#footer-text {
  float: left;
  padding-bottom: 0.5em;
}
div#footer-badges {
  float: right;
  padding-bottom: 0.5em;
}

div#preamble {
  margin-top: 1.5em;
  margin-bottom: 1.5em;
}
div.tableblock, div.imageblock, div.exampleblock, div.verseblock,
div.quoteblock, div.literalblock, div.listingblock, div.sidebarblock,
div.admonitionblock {
  margin-top: 1.0em;
  margin-bottom: 1.5em;
}
div.admonitionblock {
  margin-top: 2.0em;
  margin-bottom: 2.0em;
  margin-right: 10%;
  color: #606060;
}

div.content { /* Block element content. */
  padding: 0;
}

/* Block element titles. */
div.title, caption.title {
  color: #527bbd;
  font-weight: bold;
  text-align: left;
  margin-top: 1.0em;
  margin-bottom: 0.5em;
}
div.title + * {
  margin-top: 0;
}

td div.title:first-child {
  margin-top: 0.0em;
}
div.content div.title:first-child {
  margin-top: 0.0em;
}
div.content + div.title {
  margin-top: 0.0em;
}

div.sidebarblock > div.content {
  background: #ffffee;
  border: 1px solid #dddddd;
  border-left: 4px solid #f0f0f0;
  padding: 0.5em;
}

div.listingblock > div.content {
  border: 1px solid #dddddd;
  border-left: 5px solid #f0f0f0;
  background: #f8f8f8;
  padding: 0.5em;
}

div.quoteblock, div.verseblock {
  padding-left: 1.0em;
  margin-left: 1.0em;
  margin-right: 10%;
  border-left: 5px solid #f0f0f0;
  color: #777777;
}

div.quoteblock > div.attribution {
  padding-top: 0.5em;
  text-align: right;
}

div.verseblock > pre.content {
  font-family: inherit;
  font-size: inherit;
}
div.verseblock > div.attribution {
  padding-top: 0.75em;
  text-align: left;
}
/* DEPRECATED: Pre version 8.2.7 verse style literal block. */
div.verseblock + div.attribution {
  text-align: left;
}

div.admonitionblock .icon {
  vertical-align: top;
  font-size: 1.1em;
  font-weight: bold;
  text-decoration: underline;
  color: #527bbd;
  padding-right: 0.5em;
}
div.admonitionblock td.content {
  padding-left: 0.5em;
  border-left: 3px solid #dddddd;
}

div.exampleblock > div.content {
  border-left: 3px solid #dddddd;
  padding-left: 0.5em;
}

div.imageblock div.content { padding-left: 0; }
span.image img { border-style: none; }
a.image:visited { color: white; }

dl {
  margin-top: 0.8em;
  margin-bottom: 0.8em;
}
dt {
  margin-top: 0.5em;
  margin-bottom: 0;
  font-style: normal;
  color: navy;
}
dd > *:first-child {
  margin-top: 0.1em;
}

ul, ol {
    list-style-position: outside;
}
ol.arabic {
  list-style-type: decimal;
}
ol.loweralpha {
  list-style-type: lower-alpha;
}
ol.upperalpha {
  list-style-type: upper-alpha;
}
ol.lowerroman {
  list-style-type: lower-roman;
}
ol.upperroman {
  list-style-type: upper-roman;
}

div.compact ul, div.compact ol,
div.compact p, div.compact p,
div.compact div, div.compact div {
  margin-top: 0.1em;
  margin-bottom: 0.1em;
}

div.tableblock > table {
  border: 3px solid #527bbd;
}
thead, p.table.header {
  font-weight: bold;
  color: #527bbd;
}
tfoot {
  font-weight: bold;
}
td > div.verse {
  white-space: pre;
}
p.table {
  margin-top: 0;
}
/* Because the table frame attribute is overriden by CSS in most browsers. */
div.tableblock > table[frame="void"] {
  border-style: none;
}
div.tableblock > table[frame="hsides"] {
  border-left-style: none;
  border-right-style: none;
}
div.tableblock > table[frame="vsides"] {
  border-top-style: none;
  border-bottom-style: none;
}


div.hdlist {
  margin-top: 0.8em;
  margin-bottom: 0.8em;
}
div.hdlist tr {
  padding-bottom: 15px;
}
dt.hdlist1.strong, td.hdlist1.strong {
  font-weight: bold;
}
td.hdlist1 {
  vertical-align: top;
  font-style: normal;
  padding-right: 0.8em;
  color: navy;
}
td.hdlist2 {
  vertical-align: top;
}
div.hdlist.compact tr {
  margin: 0;
  padding-bottom: 0;
}

.comment {
  background: yellow;
}

.footnote, .footnoteref {
  font-size: 0.8em;
}

span.footnote, span.footnoteref {
  vertical-align: super;
}

#footnotes {
  margin: 20px 0 20px 0;
  padding: 7px 0 0 0;
}

#footnotes div.footnote {
  margin: 0 0 5px 0;
}

#footnotes hr {
  border: none;
  border-top: 1px solid silver;
  height: 1px;
  text-align: left;
  margin-left: 0;
  width: 20%;
  min-width: 100px;
}

div.colist td {
  padding-right: 0.5em;
  padding-bottom: 0.3em;
  vertical-align: top;
}
div.colist td img {
  margin-top: 0.3em;
}

@media print {
  div#footer-badges { display: none; }
}

div#toc {
  margin-bottom: 2.5em;
}

div#toctitle {
  color: #527bbd;
  font-size: 1.1em;
  font-weight: bold;
  margin-top: 1.0em;
  margin-bottom: 0.1em;
}

div.toclevel1, div.toclevel2, div.toclevel3, div.toclevel4 {
  margin-top: 0;
  margin-bottom: 0;
}
div.toclevel2 {
  margin-left: 2em;
  font-size: 0.9em;
}
div.toclevel3 {
  margin-left: 4em;
  font-size: 0.9em;
}
div.toclevel4 {
  margin-left: 6em;
  font-size: 0.9em;
}
</style>
<script type="text/javascript">
/*<![CDATA[*/
var asciidoc = {  // Namespace.

/////////////////////////////////////////////////////////////////////
// Table Of Contents generator
/////////////////////////////////////////////////////////////////////

/* Author: Mihai Bazon, September 2002
 * http://students.infoiasi.ro/~mishoo
 *
 * Table Of Content generator
 * Version: 0.4
 *
 * Feel free to use this script under the terms of the GNU General Public
 * License, as long as you do not remove or alter this notice.
 */

 /* modified by Troy D. Hanson, September 2006. License: GPL */
 /* modified by Stuart Rackham, 2006, 2009. License: GPL */

// toclevels = 1..4.
toc: function (toclevels) {

  function getText(el) {
    var text = "";
    for (var i = el.firstChild; i != null; i = i.nextSibling) {
      if (i.nodeType == 3 /* Node.TEXT_NODE */) // IE doesn't speak constants.
        text += i.data;
      else if (i.firstChild != null)
        text += getText(i);
    }
    return text;
  }

  function TocEntry(el, text, toclevel) {
    this.element = el;
    this.text = text;
    this.toclevel = toclevel;
  }

  function tocEntries(el, toclevels) {
    var result = new Array;
    var re = new RegExp('[hH]([2-'+(toclevels+1)+'])');
    // Function that scans the DOM tree for header elements (the DOM2
    // nodeIterator API would be a better technique but not supported by all
    // browsers).
    var iterate = function (el) {
      for (var i = el.firstChild; i != null; i = i.nextSibling) {
        if (i.nodeType == 1 /* Node.ELEMENT_NODE */) {
          var mo = re.exec(i.tagName);
          if (mo && (i.getAttribute("class") || i.getAttribute("className")) != "float") {
            result[result.length] = new TocEntry(i, getText(i), mo[1]-1);
          }
          iterate(i);
        }
      }
    }
    iterate(el);
    return result;
  }

  var toc = document.getElementById("toc");
  if (!toc) {
    return;
  }

  // Delete existing TOC entries in case we're reloading the TOC.
  var tocEntriesToRemove = [];
  var i;
  for (i = 0; i < toc.childNodes.length; i++) {
    var entry = toc.childNodes[i];
    if (entry.nodeName == 'div'
     && entry.getAttribute("class")
     && entry.getAttribute("class").match(/^toclevel/))
      tocEntriesToRemove.push(entry);
  }
  for (i = 0; i < tocEntriesToRemove.length; i++) {
    toc.removeChild(tocEntriesToRemove[i]);
  }

  // Rebuild TOC entries.
  var entries = tocEntries(document.getElementById("content"), toclevels);
  for (var i = 0; i < entries.length; ++i) {
    var entry = entries[i];
    if (entry.element.id == "")
      entry.element.id = "_toc_" + i;
    var a = document.createElement("a");
    a.href = "#" + entry.element.id;
    a.appendChild(document.createTextNode(entry.text));
    var div = document.createElement("div");
    div.appendChild(a);
    div.className = "toclevel" + entry.toclevel;
    toc.appendChild(div);
  }
  if (entries.length == 0)
    toc.parentNode.removeChild(toc);
},


/////////////////////////////////////////////////////////////////////
// Footnotes generator
/////////////////////////////////////////////////////////////////////

/* Based on footnote generation code from:
 * http://www.brandspankingnew.net/archive/2005/07/format_footnote.html
 */

footnotes: function () {
  // Delete existing footnote entries in case we're reloading the footnodes.
  var i;
  var noteholder = document.getElementById("footnotes");
  if (!noteholder) {
    return;
  }
  var entriesToRemove = [];
  for (i = 0; i < noteholder.childNodes.length; i++) {
    var entry = noteholder.childNodes[i];
    if (entry.nodeName == 'div' && entry.getAttribute("class") == "footnote")
      entriesToRemove.push(entry);
  }
  for (i = 0; i < entriesToRemove.length; i++) {
    noteholder.removeChild(entriesToRemove[i]);
  }

  // Rebuild footnote entries.
  var cont = document.getElementById("content");
  var spans = cont.getElementsByTagName("span");
  var refs = {};
  var n = 0;
  for (i=0; i<spans.length; i++) {
    if (spans[i].className == "footnote") {
      n++;
      var note = spans[i].getAttribute("data-note");
      if (!note) {
        // Use [\s\S] in place of . so multi-line matches work.
        // Because JavaScript has no s (dotall) regex flag.
        note = spans[i].innerHTML.match(/\s*\[([\s\S]*)]\s*/)[1];
        spans[i].innerHTML =
          "[<a id='_footnoteref_" + n + "' href='#_footnote_" + n +
          "' title='View footnote' class='footnote'>" + n + "</a>]";
        spans[i].setAttribute("data-note", note);
      }
      noteholder.innerHTML +=
        "<div class='footnote' id='_footnote_" + n + "'>" +
        "<a href='#_footnoteref_" + n + "' title='Return to text'>" +
        n + "</a>. " + note + "</div>";
      var id =spans[i].getAttribute("id");
      if (id != null) refs["#"+id] = n;
    }
  }
  if (n == 0)
    noteholder.parentNode.removeChild(noteholder);
  else {
    // Process footnoterefs.
    for (i=0; i<spans.length; i++) {
      if (spans[i].className == "footnoteref") {
        var href = spans[i].getElementsByTagName("a")[0].getAttribute("href");
        href = href.match(/#.*/)[0];  // Because IE return full URL.
        n = refs[href];
        spans[i].innerHTML =
          "[<a href='#_footnote_" + n +
          "' title='View footnote' class='footnote'>" + n + "</a>]";
      }
    }
  }
},

install: function(toclevels) {
  var timerId;

  function reinstall() {
    asciidoc.footnotes();
    if (toclevels) {
      asciidoc.toc(toclevels);
    }
  }

  function reinstallAndRemoveTimer() {
    clearInterval(timerId);
    reinstall();
  }

  timerId = setInterval(reinstall, 500);
  if (document.addEventListener)
    document.addEventListener("DOMContentLoaded", reinstallAndRemoveTimer, false);
  else
    window.onload = reinstallAndRemoveTimer;
}

}
asciidoc.install();
/*]]>*/
</script>
</head>
<body class="article">
<div id="header">
<h1>挑战开发组</h1>
</div>
<div id="content">
<div id="preamble">
<div class="sectionbody">
<div class="listingblock">
<div class="title"><br>身份配置</div>
<div class="content">
<pre><tt>主公：士兵
反贼：四个士兵
</tt></pre>
</div></div>
<div class="listingblock">
<div class="title">行动顺序</div>
<div class="content">
<pre><tt>5人局，人类玩家必定是主公</tt></pre>
</div></div>
<div class="listingblock">
<div class="title">特殊规则</div>
<div class="content">
<pre><tt>游戏开始前：
1.反贼依次变为一名随机开发组角色
2.主公可以选择变为女性
3.主公选择一个势力
4.主公从随机出现的五个源码技能中选择一个获得，重复三次。只出现自己势力的
技能，不会出现主公技、觉醒技、隐匿技和已拥有的技能。

当一个反贼死亡时，主公获得其武将牌上所有技能，回复2点体力，摸两张牌

本模式禁用【桃园结义】

本模式禁用作弊功能</tt></pre>
</div></div>
</div>
</div>
<div class="sect1">
<h2 id="_剧情">剧情</h2><br>
<div class="sectionbody">
<div class="sidebarblock">
<div class="content">
<div class="title">萌新的困惑</div>
<div class="paragraph"><p>第奇数轮开始时：若存活角色数大于2，体力值最高的一名开发组角色失去1点体力，若你是体力值最低的角色，重复一次。</p></div>
</div></div>
<div class="sidebarblock">
<div class="content">
<div class="title">大神的怒火</div>
<div class="paragraph"><p>第偶数轮开始时：你弃置装备区内所有牌，所有开发组角色攻击范围+1。</p></div>
</div></div>
</div>
</div>

<h2 id="_剧情">开发组成员</h2><br>
<div class="sectionbody">
<div class="sidebarblock">
<div class="content">
<div class="title">以下只是一部分开发组成员，还有的大神我实在是记不得了，万分抱歉(＞人＜；)因种种原因，他们逐渐销声匿迹，但是他们为神杀作出的这许多贡献，我们会永远记得！我在此献上最崇高的敬意！</div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">Xusineday（数字君）  2勾玉  女性  技能设计：Xusineday  （数字神在自己的lua包中是女性。可惜因ai原因，数字神不会出现在游戏中）</div>
<div class="paragraph"><p>略懂：当你需要使用或打出基本牌时，你可展示牌堆顶2+X张牌（X为你已损失的体力值），然后使用或打出其中的此牌。这些牌中，你可以将不为此牌的黑桃牌当【酒】，以下同理：方块当【杀】，梅花牌当【闪】。
<div class="paragraph"><p>猜疑：当你使用【杀】指定一名角色为目标后，你可令其选择一项：弃置一张【杀】；或不能响应此【杀】。
</p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">独孤安河  4勾玉  男性  技能设计：独孤安河＆Xusineday</div>
<div class="paragraph"><p>箭矢：当你因弃置而失去黑色牌后，你可将在弃牌堆中的这些牌置于你的武将牌上称为“箭”。回合外，你可将两张“箭”当【无懈可击】使用。
<div class="paragraph"><p>藏刀：当你的一张装备牌进入弃牌堆前，你可将其交给一名角色，然后你可视为对其使用一张【杀】（不计次）。
</p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">虫妹  3勾玉  女性  技能设计：虫妹＆Xusineday</div>
<div class="paragraph"><p>翩跹：<font color="blue"><b>锁定技，</b></font>游戏开始时，你获得三枚“蝶”标记。当你于回合外获得或失去牌后，你获得等量的“蝶”标记。
<div class="paragraph"><p>全能：你可以弃置一枚“蝶”标记来发动以下技能：“枭姬”，“界集智”，“补益”，“淑慎”，“烈刃”。
</p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">嘟嘟  4勾玉  男性  技能设计：嘟嘟＆Xusineday</div>
<div class="paragraph"><p>破风：<font color="green"><b>出牌阶段限一次，</b></font>你可摸一张牌并弃置一张牌，然后令其他角色各摸一张牌并弃置一张牌，以此法弃置牌点数大于你的角色回复1点体力，点数小于你的角色失去1点体力。
<div class="paragraph"><p>销魂：<font color="red"><b>限定技，</b></font>出牌阶段，你可将任意数量的手牌正面朝上交给相同数量的其他角色。若一名角色以此法获得的牌点数是其体力值的整数倍，你弃置其两张牌并对其造成1点火焰伤害；若不为整数倍，该角色摸两张牌。
</p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">doublebit  3勾玉  男性  技能设计：doublebit＆Xusineday  （其实在数字神的lua包中是女性哦）</div>
<div class="paragraph"><p>塞车：其他角色的回合开始时，你可以交给该角色一张手牌，然后本回合该角色使用牌只能指定距离不大于1的角色为目标。
<div class="paragraph"><p>爪机：<font color="blue"><b>锁定技，</b></font>结束阶段开始时，你按以下规则获得相应技能直到下回合开始（X为你本回合出牌阶段使用的牌数）：若X小于1，你获得技能“120”；若X等于1，你获得技能“119”；若X大于1，你获得技能“110”。
<div class="paragraph"><p>110：<font color="blue"><b>锁定技，</b></font>当你于回合外第一次受到伤害时，伤害来源将武将牌翻面。
<div class="paragraph"><p>119：<font color="blue"><b>锁定技，</b></font>当你于回合外第一次受到属性伤害时，防止此伤害。
<div class="paragraph"><p>120：<font color="blue"><b>锁定技，</b></font>回合结束时，你复原武将牌，然后回复1点体力。
</p></div></p></div></p></div></p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">阿米拉嗪  3勾玉  女性  技能设计：阿米拉嗪＆Xusineday  （贴吧性别是女性，但是在数字神的lua包中是男性）</div>
<div class="paragraph"><p>假药：<font color="blue"><b>锁定技，</b></font>当你于出牌阶段内对一名其他角色造成伤害后，该角色选项一项：弃置一张手牌；或下一个摸牌阶段少摸一张牌。
<div class="paragraph"><p>上瘾：当一名角色于你的回合内进入濒死状态时，你可令一名角色摸一张牌，然后本回合你可额外使用一张【杀】。
</p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">myetyet（橙子君）  3勾玉  男性  技能设计：叫什么啊你妹</div>
<div class="paragraph"><p>鲜橙：结束阶段开始时，你可以获得弃牌堆内的一张【杀】。
<div class="paragraph"><p>橙汁：当一名角色处于濒死状态时，你可以将一张【杀】正面向上置于牌堆顶，该角色将体力回复至1点。
<div class="paragraph"><p>半橙：当你受到1点伤害后或<font color="green"><b>出牌阶段限一次，</b></font>你可以指定一名其他角色并翻开牌堆顶的X+2张牌（X为你已损失的体力值），你对其造成等同于其中【杀】数量的伤害，然后该角色获得其中一张牌并将其余牌置入弃牌堆。
</p></div></p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">医治永恒  3/5  男性  技能设计：叫什么啊你妹</div>
<div class="paragraph"><p>曙光：当一名开发组角色进入濒死状态时，若你的体力上限不小于存活角色数，你可以减1点体力上限，然后该角色回复所有体力。
</p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">元嘉体  2勾玉  男性  技能设计：叫什么啊你妹</div>
<div class="paragraph"><p>暴击：<font color="blue"><b>锁定技，</b></font>当你造成伤害时，你有75%的几率令此伤害+1。
<div class="paragraph"><p>闪避：<font color="blue"><b>锁定技，</b></font>当你受到伤害时，你有75%的几率防止此伤害。
</p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">付尼玛  3勾玉  男性  技能设计：叫什么啊你妹  （其实我很想把妮妮设计成女性）</div>
<div class="paragraph"><p>妮妮：<font color="green"><b>出牌阶段限一次，</b></font>你可以用牌堆顶的一张牌与一名角色拼点，若你赢，你获得弃牌堆内你的拼点牌且本回合你的攻击范围+1。
<div class="paragraph"><p>蛋疼：当一名角色于其出牌阶段内造成伤害后，你可以摸一张牌，然后若该角色不为你且未受伤，其结束出牌阶段。
</p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">Paracel_007  3勾玉  男性  技能设计：叫什么啊你妹</div>
<div class="paragraph"><p>更新：<font color="green"><b>本技能有90秒冷却时间，</b></font>结束阶段开始时，你可以弃置一名角色装备区里的武器牌或将一张武器牌置入一名角色的装备区，然后你获得等同于此牌攻击范围的护甲。
<div class="paragraph"><p>学霸：<font color="green"><b>对每名角色限一次，</b></font>当一名角色进入濒死状态时，你可以对其使用牌堆内的第一张【桃】，然后你发现一个神势力武将的技能。
</p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">rara  3勾玉  女性  技能设计：叫什么啊你妹</div>
<div class="paragraph"><p>魅惑：当你成为【杀】的目标后，你可以重铸此【杀】并展示手牌中以此法获得的牌，若此牌是基本牌，此【杀】对你无效。当你使用的【杀】命中目标角色后，你可以重铸其一张手牌。
<div class="paragraph"><p>女神：当你受到1点伤害后，你可以将一名体力值大于1的角色的1点体力转化为1点护甲；你造成的伤害无视护甲。
</p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">Fsu0413  5勾玉  男性  技能设计：Fsu0413</div>
<div class="paragraph"><p>嗝屁：其他角色的准备阶段开始时，你可以令其弃置你的一张牌，若如此做，你令其一个技能（主公技、觉醒技除外）于此回合内无效。若以此法无效描述中不含有“出牌阶段”的技能或不能无效技能，其摸三张牌。
</p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">hmqgg  4勾玉  男性  技能设计：hmqgg  （技能来源于萌战MOD）</div>
<div class="paragraph"><p>柴刀：当你使用【杀】对一名角色造成伤害时，你可以弃置你装备区里的武器牌，令此伤害+1。当你受到其他角色造成的伤害时，若你的装备区里有武器牌，你可以防止此伤害，然后其视为对你使用一张【借刀杀人】，【杀】的目标为该角色。
</p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">takashiro（高城君）  4勾玉  男性  技能设计：Notify  （技能来源于脑洞包）</div>
<div class="paragraph"><p>骚动：当你受到锦囊牌造成的伤害时，你可交给伤害来源一张<font color="red"><b>♦</b></font>牌，然后将此伤害转移给其。当一名其他角色受到伤害来源不为你的伤害后，你可以将一张<font color="red"><b>♥</b></font>牌当【桃】或【无中生有】使用。
</p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">lzxqqqq（月兔）  3勾玉  女性  技能设计：lzxqqqq  （技能来源于亚山之殇MOD。贴吧性别是女性）</div>
<div class="paragraph"><p>治愈：摸牌阶段，你可以放弃摸牌，改为令一名已受伤的其他角色回复1点体力，然后你摸X张牌（X为其已损失的体力值且至多为2）。
<div class="paragraph"><p>平和：<font color="blue"><b>锁定技，</b></font>当你使用【杀】对目标角色造成伤害后，若其与你势力不同，你弃置其一张手牌，然后若其手牌数：不小于其体力值，你回复1点体力；小于其体力值，你摸一张牌。
</p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">开不了车  4勾玉  男性  技能设计：开不了车  （技能来源于洛吧杀。车神，我学写神杀lua道路上对我帮助最大的大神！）</div>
<div class="paragraph"><p>指引：摸牌阶段摸牌时，你可以弃置一名其他角色判定区内的一张牌，然后其摸一张牌且你少摸一张牌。
<div class="paragraph"><p>教导：当一名其他角色使用的非延时锦囊牌结算完时，若其存活且此牌的目标角色数大于0，你可以令此牌对相同目标再次生效（【借刀杀人】、【铁索连环】、【五谷丰登】除外）。
</p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">36李  3勾玉  男性  技能设计：叫什么啊你妹</div>
<div class="paragraph"><p>美工：<font color="green"><b>出牌阶段限一次，</b></font>你可以令一名其他角色的限定技的可发动次数+1。所有角色的结束阶段开始时会触发两次。
<div class="paragraph"><p>弃疗：结束阶段开始时，若弃牌堆里有点数为X的整数倍的牌（X为你的体力值且至少为1），你可以令一名其他角色摸一张牌，然后你可以令其回复1点体力，若如此做，你下一次的回复效果无效。
</p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">任意角的正切  3勾玉  男性  技能设计：Notify  （技能来源于脑洞包）</div>
<div class="paragraph"><p>学习：当你于你的出牌阶段内使用牌时，你可以展示牌堆顶的一张牌，若你本回合未以此法获得过该类型的牌，你获得之。
<div class="paragraph"><p>愉快：<font color="blue"><b>锁定技，</b></font>弃牌阶段结束时，你摸X张牌（X为你手牌中缺少的类型数且至少为1）。
</p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">张拯明寰  4勾玉  男性  技能设计：叫什么啊你妹</div>
<div class="paragraph"><p>给力：当你回复体力后，你获得一枚“制图”标记。一名角色的回合开始时，你可以弃置一枚“制图”标记，令其本回合的回复效果改为造成等量伤害。
</p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">陈家祺大同中心  3勾玉  男性  技能设计：叫什么啊你妹</div>
<div class="paragraph"><p>酱油：<font color="blue"><b>锁定技，</b></font>游戏开始时，你休眠。所有角色累计使用X张锦囊牌后，若你：处于休眠状态，将你唤醒（X为5）；不处于休眠状态，你休眠（X为10）。当一名开发组角色死亡时，若存活角色数为2，将你唤醒且你失去此技能。
<div class="paragraph"><p>合体：<font color="blue"><b>锁定技，</b></font>当你被唤醒后，你的手牌上限+1，然后武将牌为“付尼玛”的其他角色的手牌上限+1。当武将牌为“付尼玛”的其他角色死亡时，若你没有副将或你的副将不是“付尼玛”，你将副将变为“付尼玛”（休眠状态下不触发）。
</p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">ZY  3勾玉  男性  技能设计：Notify  （技能来源于脑洞包。虽然不是老开发组成员，但在老开发组成员逐渐退坑后，毅然承担起了更新的重责）</div>
<div class="paragraph"><p>奆佬：<font color="green"><b>出牌阶段限一次，</b></font>你可以视为使用本回合你使用过的上一张非转化的非延时类锦囊牌。
<div class="paragraph"><p>夜更：<font color="blue"><b>锁定技，</b></font>回合结束后，若你本回合使用的非延时类锦囊牌数不小于3，你进行一个额外回合。
</p></div></p></div>
</div></div>

<div class="sidebarblock">
<div class="content">
<div class="title">饺神  4勾玉  男性  技能设计：饺神  （技能来源于高达杀。虽然不是老开发组成员，也没更新过神杀，但……谁不想吃饺呢？）</div>
<div class="paragraph"><p>饺气：当你使用【杀】指定目标后，若你的装备区有：武器牌或防具牌，你可弃置其装备区内的一张牌；坐骑牌，你可令其抵消此【杀】的方式改为依次使用两张【闪】。
<div class="paragraph"><p>傲饺：<font color="purple"><b>觉醒技，</b></font>准备阶段开始时或当你成为【杀】的目标时，若你已受伤且装备区内有牌，你减1点体力上限，然后获得技能“饺猾”。
<div class="paragraph"><p>饺猾：当你成为【杀】的目标后，你可以将一张与装备区内的牌花色相同的手牌当【杀】使用，若如此做，移除此【杀】的所有目标。
</p></div></p></div></p></div>
</div></div>

<div class="sect1">
<h2 id="_附注">附注</h2><br>
<div class="sectionbody">
<div class="paragraph"><p>这是一个难度逐渐降低的模式，你将在开发组大神的“教导”下，完成从萌新到大神的蜕变。</p></div>
</div>
</div>

<div id="footer">
<div id="footer-text">
Last updated 2022-01-09 02:25:45 中国标准时间
</div>
</div>
</body>
</html>
