
sgs.ai_skill_askforyiji.kesxjimeng = function(self,card_ids,tos)
    local p,i = sgs.ai_skill_askforyiji.nosyiji(self,card_ids,tos)
	if not p and self.player:getPhase()~=sgs.Player_Start then
		p = tos:at(0)
		i = card_ids[#card_ids]
	end
	return p,i
end

sgs.ai_skill_playerschosen.kesxhehe = function(self,players)
    local tos = {}
	local destlist = sgs.QList2Table(players) -- 将列表转换为表
	self:sort(destlist)
	for _,p in sgs.list(destlist)do
		if self:isFriend(p) and #tos<2 then table.insert(tos,p) end
	end
	for _,p in sgs.list(destlist)do
		if not self:isEnemy(p) and #tos<2 and #self.enemies>0 and not table.contains(tos,p)
		then table.insert(tos,p) end
	end
	return tos
end

sgs.ai_fill_skill.kesxquedi = function(self)
    local cards = self.player:getCards("he")
    cards = sgs.QList2Table(cards) -- 将列表转换为表
    self:sortByKeepValue(cards) -- 按保留值排序
	for _,c in sgs.list(cards)do
		if c:isKindOf("Slash") then
			local can = dummyCard("duel")
			can:setSkillName("kesxquedi")
			can:addSubcard(c)
			if can:isAvailable(self.player) then
				return can
			end
		end
	end
end

sgs.ai_skill_playerchosen.kesxchunlao = function(self,players)
	local destlist = sgs.QList2Table(players) -- 将列表转换为表
	self:sort(destlist,"card")
	local tag = self.player:getTag("kesxchunlaoToGet"):toIntList()
    for _,target in sgs.list(destlist)do
		if self:isFriend(target)
		and target:getHandcardNum()<tag:length()
		then return target end
	end
    for _,target in sgs.list(destlist)do
		if self:isEnemy(target)
		and target:getHandcardNum()>=tag:length()
		then return target end
	end
    for _,target in sgs.list(destlist)do
		if not self:isFriend(target)
		then return target end
	end
end

sgs.ai_skill_invoke.kesxchunlao = function(self,data)
	local srt = data:toString():split(":")
	local to = BeMan(self.room,srt[2])
	if to then
		return self:isFriend(to) or not self:isEnemy(to) and #self.enemies>0
	end
end

sgs.ai_skill_playerschosen.kesxxiongsuan = function(self,players)
	local destlist = sgs.QList2Table(players) -- 将列表转换为表
	self:sort(destlist)
	local tos = {}
    for _,p in sgs.list(destlist)do
		if self:isEnemy(p) then table.insert(tos,p) end
	end
	self:sort(destlist,nil,true)
    for _,p in sgs.list(destlist)do
		if #tos<2 and not table.contains(tos,p)
		and not self:isFriend(p)
		then table.insert(tos,p) end
	end
    for _,p in sgs.list(destlist)do
		if #tos<1 then table.insert(tos,p) end
	end
	return tos
end

sgs.ai_fill_skill.kesxtiaohe = function(self)
	return sgs.Card_Parse("#kesxtiaoheCard:.:")
end

sgs.ai_skill_use_func["#kesxtiaoheCard"] = function(card,use,self)
	self:sort(self.friends)
	for _,ep in sgs.list(self.friends)do
		if use.to:isEmpty() then
			local w = ep:getWeapon()
			if w and self:doDisCard(ep,w:getEffectiveId()) then
				use.to:append(ep)
			end
		else
			local a = ep:getArmor()
			if a and self:doDisCard(ep,a:getEffectiveId()) then
				use.to:append(ep)
				use.card = card
				return
			end
		end
	end
	for _,ep in sgs.list(self.friends)do
		if use.to:isEmpty() then
			local w = ep:getWeapon()
			if w and self:doDisCard(ep,w:getEffectiveId()) then
				use.to:append(ep)
			end
		else
			local a = ep:getArmor()
			if a and self:doDisCard(ep,a:getEffectiveId()) then
				use.to:append(ep)
				use.card = card
				return
			end
		end
	end
	self:sort(self.enemies)
	for _,ep in sgs.list(self.enemies)do
		if use.to:isEmpty() then
			local w = ep:getWeapon()
			if w and self:doDisCard(ep,w:getEffectiveId()) then
				use.to:append(ep)
			end
		else
			local a = ep:getArmor()
			if a and self:doDisCard(ep,a:getEffectiveId()) then
				use.to:append(ep)
				use.card = card
				return
			end
		end
	end
	for _,ep in sgs.list(self.enemies)do
		if use.to:isEmpty() then
			local w = ep:getWeapon()
			if w and self:doDisCard(ep,w:getEffectiveId()) then
				use.to:append(ep)
			end
		else
			local a = ep:getArmor()
			if a and self:doDisCard(ep,a:getEffectiveId()) then
				use.to:append(ep)
				use.card = card
				return
			end
		end
	end
	for _,ep in sgs.list(self.room:getAlivePlayers())do
		if self:isFriend(ep) then continue end
		if use.to:isEmpty() then
			if ep:getWeapon() then
				use.to:append(ep)
			end
		else
			if ep:getArmor() then
				use.to:append(ep)
				use.card = card
				return
			end
		end
	end
end

sgs.ai_use_value.kesxtiaoheCard = 7.4
sgs.ai_use_priority.kesxtiaoheCard = 6.2

sgs.ai_skill_invoke.kesxqiansu = function(self,data)
	return self:canDraw()
end

sgs.ai_fill_skill.kesxbazhan = function(self)
    local cards = self.player:getCards("h")
    cards = sgs.QList2Table(cards) -- 将列表转换为表
    self:sortByKeepValue(cards) -- 按保留值排序
	self:sort(self.enemies)
	for _,c in sgs.list(cards)do
	   	if table.contains(self.toUse,c) then continue end
		for _,ep in sgs.list(self.enemies)do
			if ep:getMark("&kesxqiaoying-Clear") == ep:getHandcardNum() and self.player:inMyAttackRange(ep) then
				self.ht_to = ep
				return sgs.Card_Parse("#kesxbazhanCard:"..c:getEffectiveId()..":")
			end
		end
	end
	for _,c in sgs.list(cards)do
	   	if table.contains(self.toUse,c) then continue end
		for _,ep in sgs.list(self.enemies)do
			if ep:getMark("&kesxqiaoying-Clear") == ep:getHandcardNum() then
				self.ht_to = ep
				return sgs.Card_Parse("#kesxbazhanCard:"..c:getEffectiveId()..":")
			end
		end
	end
	for _,c in sgs.list(cards)do
	   	if table.contains(self.toUse,c) then continue end
		for _,ep in sgs.list(self.friends_noself)do
			if ep:getMark("&kesxqiaoying-Clear") > ep:getHandcardNum() then
				self.ht_to = ep
				return sgs.Card_Parse("#kesxbazhanCard:"..c:getEffectiveId()..":")
			end
		end
	end
	for _,c in sgs.list(cards)do
	   	if table.contains(self.toUse,c) then continue end
		for _,ep in sgs.list(self.friends_noself)do
			if ep:getMark("&kesxqiaoying-Clear") <= ep:getHandcardNum() then
				self.ht_to = ep
				return sgs.Card_Parse("#kesxbazhanCard:"..c:getEffectiveId()..":")
			end
		end
	end
end

sgs.ai_skill_use_func["#kesxbazhanCard"] = function(card,use,self)
	use.card = card
	use.to:append(self.ht_to)
end

sgs.ai_use_value.kesxbazhanCard = 7.4
sgs.ai_use_priority.kesxbazhanCard = 2.2

sgs.ai_skill_invoke.kesxyibing = function(self,data)
	local target = data:toPlayer()
	if target then
		if self:isFriend(target) then return self:doDisCard(target,"e",true)
		else return self:doDisCard(target,"he",true) end
	end
end

sgs.ai_skill_use["@@kesxzhiyi"] = function(self,prompt)
	local dc = dummyCard()
	dc:setSkillName("_kesxzhiyi")
    local dummy = self:aiUseCard(dc)
   	if dummy.card and dummy.to then
      	local tos = {}
       	for _,p in sgs.list(dummy.to)do
       		table.insert(tos,p:objectName())
       	end
       	return dc:toString().."->"..table.concat(tos,"+")
    end
end

sgs.ai_skill_playerchosen.kesxdaoshu = function(self,players)
	local destlist = sgs.QList2Table(players) -- 将列表转换为表
	self:sort(destlist)
	local cp = self.room:getCurrent()
	if self:isEnemy(cp) then
		for _,p in sgs.list(destlist)do
			if self:isEnemy(p)
			then return p end
		end
		for _,p in sgs.list(destlist)do
			if not self:isFriend(p)
			then return p end
		end
	else
		for _,p in sgs.list(destlist)do
			if self:isEnemy(p)
			and cp:inMyAttackRange(p)
			and p:getHandcardNum()<3
			then return p end
		end
	end
end


sgs.ai_skill_playerchosen.kesxfenghun = function(self,players)
	local destlist = sgs.QList2Table(players) -- 将列表转换为表
	for _,p in sgs.list(destlist)do
		if not self:isFriend(p) then
			for _,e in sgs.list(p:getCards("e"))do
				if e:getSuit() == sgs.Card_Diamond and self:doDisCard(p,e:getEffectiveId()) then
					return p
				end
			end
			for _,e in sgs.list(p:getCards("e"))do
				if e:getSuit() == sgs.Card_Diamond then
					return p
				end
			end
		end
	end
	for _,p in sgs.list(destlist)do
		if self:isFriend(p) then
			for _,e in sgs.list(p:getCards("he"))do
				if e:getSuit() == sgs.Card_Diamond and self:doDisCard(p,e:getEffectiveId()) then
					return p
				end
			end
			for _,e in sgs.list(p:getCards("he"))do
				if e:getSuit() == sgs.Card_Diamond then
					return p
				end
			end
		end
	end
	for _,p in sgs.list(destlist)do
		if not self:isFriend(p) and self:doDisCard(p,"he") then
			return p
		end
	end
end


sgs.ai_skill_cardchosen.kesxfenghun = function(self,who,flags,method)
	if who:objectName()==self.player:objectName() then
		local cards = self:sortByKeepValue(who:getCards("he"))
		for _,e in sgs.list(cards)do
			if e:getSuit() == sgs.Card_Diamond and self:doDisCard(who,e:getEffectiveId()) then
				return e:getEffectiveId()
			end
		end
		for _,e in sgs.list(cards)do
			if e:getSuit() == sgs.Card_Diamond then
				return e:getEffectiveId()
			end
		end
	else
		for _,e in sgs.list(who:getCards("e"))do
			if e:getSuit() == sgs.Card_Diamond and self:doDisCard(who,e:getEffectiveId()) then
				return e:getEffectiveId()
			end
		end
		for _,e in sgs.list(who:getCards("e"))do
			if e:getSuit() == sgs.Card_Diamond then
				return e:getEffectiveId()
			end
		end
	end
end



sgs.ai_fill_skill.kesxxiongyi = function(self)
	return sgs.Card_Parse("#kesxxiongyiCard:.:")
end

sgs.ai_skill_use_func["#kesxxiongyiCard"] = function(card,use,self)
	self:sort(self.friends,nil,true)
	for _,p in sgs.list(self.friends)do
		if getKnownCard(p,self.player,"Slash")>0 then
			for _,ep in sgs.list(self.enemies)do
				if p:canSlash(ep) then
					use.to:append(ep)
					if use.to:length()>#self.friends/2 and sgs.turncount>1 then
						use.card = card
					end
					break
				end
			end
		end
	end
end

sgs.ai_use_value.kesxxiongyiCard = 5.4
sgs.ai_use_priority.kesxxiongyiCard = 0.2
sgs.ai_card_intention.kesxxiongyiCard = -77




sgs.ai_skill_use["@@kesxyouqi"] = function(self,prompt)
	local valid = {}
	local destlist = self.room:getAllPlayers()
    destlist = sgs.QList2Table(destlist) -- 将列表转换为表
    self:sort(destlist)
	for _,p in sgs.list(destlist)do
    	if self:isFriend(p) and p:getKingdom() == "qun" then
			for _,e in sgs.list(self:poisonCards("e",p))do
				if e:isKindOf("Horse") then
					local n = e:getRealCard():toEquipCard():location()
					for _,q in sgs.list(destlist)do
						if self:isEnemy(q)and q:hasEquipArea(n) and not q:getEquip(n) then
							self.kesxyouqiEid = e:getId()
							table.insert(valid,p:objectName())
							table.insert(valid,q:objectName())
							return string.format("#kesxyouqiCard:.:->%s",table.concat(valid,"+"))
						end
					end
					for _,q in sgs.list(destlist)do
						if not self:isFriend(q)and q:hasEquipArea(n) and not q:getEquip(n) then
							self.kesxyouqiEid = e:getId()
							table.insert(valid,p:objectName())
							table.insert(valid,q:objectName())
							return string.format("#kesxyouqiCard:.:->%s",table.concat(valid,"+"))
						end
					end
				end
			end
		end
	end
	for _,p in sgs.list(destlist)do
    	if self:isEnemy(p) and p:getKingdom() == "qun" then
			for _,e in sgs.list(p:getEquips())do
				if e:isKindOf("Horse") and #self:poisonCards({e},p)<1 then
					local n = e:getRealCard():toEquipCard():location()
					for _,q in sgs.list(destlist)do
						if self:isFriend(q)and q:hasEquipArea(n) and not q:getEquip(n) then
							self.kesxyouqiEid = e:getId()
							table.insert(valid,p:objectName())
							table.insert(valid,q:objectName())
							return string.format("#kesxyouqiCard:.:->%s",table.concat(valid,"+"))
						end
					end
					for _,q in sgs.list(destlist)do
						if not self:isEnemy(q)and q:hasEquipArea(n) and not q:getEquip(n) then
							self.kesxyouqiEid = e:getId()
							table.insert(valid,p:objectName())
							table.insert(valid,q:objectName())
							return string.format("#kesxyouqiCard:.:->%s",table.concat(valid,"+"))
						end
					end
				end
			end
		end
	end
	for _,p in sgs.list(destlist)do
    	if not self:isFriend(p) and p:getKingdom() == "qun" then
			for _,e in sgs.list(p:getEquips())do
				if e:isKindOf("Horse") and #self:poisonCards({e},p)<1 then
					local n = e:getRealCard():toEquipCard():location()
					for _,q in sgs.list(destlist)do
						if self:isFriend(q)and q:hasEquipArea(n) and not q:getEquip(n) then
							self.kesxyouqiEid = e:getId()
							table.insert(valid,p:objectName())
							table.insert(valid,q:objectName())
							return string.format("#kesxyouqiCard:.:->%s",table.concat(valid,"+"))
						end
					end
					for _,q in sgs.list(destlist)do
						if not self:isEnemy(q) and q:hasEquipArea(n) and not q:getEquip(n) then
							self.kesxyouqiEid = e:getId()
							table.insert(valid,p:objectName())
							table.insert(valid,q:objectName())
							return string.format("#kesxyouqiCard:.:->%s",table.concat(valid,"+"))
						end
					end
				end
			end
		end
	end
end

sgs.ai_skill_cardchosen.kesxfenghun = function(self,who,flags,method)
	if self.kesxyouqiEid then
		return self.kesxyouqiEid
	end
end


sgs.ai_fill_skill.kesxmingfa = function(self)
	return sgs.Card_Parse("#kesxmingfaCard:.:")
end

sgs.ai_skill_use_func["#kesxmingfaCard"] = function(card,use,self)
	self:sort(self.enemies)
	for _,p in sgs.list(self.enemies)do
		if p:getHp()>1 and self:damageIsEffective(p,"N") then
			use.to:append(p)
			use.card = card
			break
		end
	end
end

sgs.ai_use_value.kesxmingfaCard = 5.4
sgs.ai_use_priority.kesxmingfaCard = 0.2
sgs.ai_card_intention.kesxmingfaCard = 77

sgs.ai_skill_playerschosen.kesxhuiji = function(self,players)
    local tos = {}
	local use = self.player:getTag("kesxhuijiUse"):toCardUse()
	local d = self:aiUseCard(use.card,dummy(true,1,use.to))
	if d.card then
		for _,p in sgs.list(players)do
			if d.to:contains(p) and #tos<2
			then table.insert(tos,p) end
		end
	end
	return tos
end


sgs.ai_fill_skill.kesxxiongxia = function(self)
    local cards = self.player:getCards("he")
    cards = sgs.QList2Table(cards) -- 将列表转换为表
    self:sortByKeepValue(cards) -- 按保留值排序
	for _,c1 in sgs.list(cards)do
		for _,c2 in sgs.list(cards)do
			if c1==c2 then continue end
			local dc = dummyCard("duel")
			dc:addSubcard(c1)
			dc:addSubcard(c2)
			dc:setSkillName("kesxxiongxia")
			local d = self:aiUseCard(dc,dummy(nil,1))
			if d.card then
				local valid = {}
				table.insert(valid,c1:getEffectiveId())
				table.insert(valid,c2:getEffectiveId())
				self.kesxxiongxia_to = d.to
				return sgs.Card_Parse("#kesxxiongxiaCard:"..table.concat(valid,"+")..":")
			end
		end
	end
end

sgs.ai_skill_use_func["#kesxxiongxiaCard"] = function(card,use,self)
	if self.kesxxiongxia_to then
		use.card = card
		use.to = self.kesxxiongxia_to
	end
end

sgs.ai_use_value.kesxxiongxiaCard = 5.4
sgs.ai_use_priority.kesxxiongxiaCard = 2.2
sgs.ai_card_intention.kesxxiongxiaCard = 77


sgs.ai_skill_invoke.kesxjinjian = function(self,data)
	local str = data:toString():split(":")
	if str[1]=="kesxjinjian0" then
		local to = BeMan(self.room,str[2])
		return self:isFriend(to)
	else
		local to = BeMan(self.room,str[2])
		return self:isEnemy(to) or not self:isFriend(to) and #self.enemies<1
	end
end








