--曹操
sgs.ai_skill_invoke.keqizhenglue = function(self, data)
	return true
end
sgs.ai_skill_invoke.keqizhengluegaincard = function(self, data)
	return true
end

sgs.ai_skill_playerschosen.keqizhenglue = function(self, targets, max, min)
    local selected = sgs.SPlayerList()
    local n = max
    local can_choose = sgs.QList2Table(targets)
    self:sort(can_choose, "defense")
	--优先敌人
    for _,target in ipairs(can_choose) do
        if self:isEnemy(target) and not selected:contains(target) then
            selected:append(target)
            n = n - 1
        end
        if n <= 0 then break end
    end
	--没选满，任何人都可以
	if (n > 0) then
		for _,target in ipairs(can_choose) do
			if not selected:contains(target) then
				selected:append(target)
				n = n - 1
			end
			if n <= 0 then break end
		end
	end
    return selected
end

sgs.ai_skill_playerchosen.keqizhenglue = function(self, targets)
	targets = sgs.QList2Table(targets)
	local num = 1
	for _, p in ipairs(targets) do
		if not (p:objectName() == self.player:objectName()) then
			num = 0
			return p
		end
	end
	if num == 1 then
		return self
	end
	return nil
end
--[[
sgs.ai_skill_playerschosen.keqizhenglue = function(self,players,x,n)
	local destlist = players
    destlist = sgs.QList2Table(destlist) -- 将列表转换为表
	--self:sort(destlist,"hp")
	local tos = {}
	for _,to in sgs.list(destlist)do
        table.insert(tos,to) end
	return tos
end
]]
sgs.ai_skill_playerchosen.keqipingrong = function(self, targets)
	for _, p in ipairs(sgs.QList2Table(targets)) do
		if self:isFriend(p) then
			return p
		end
	end
	for _, p in ipairs(sgs.QList2Table(targets)) do
		if not self:isEnemy(p) then
			return p
		end
	end
end

--刘备
sgs.ai_skill_playerchosen.keqijishan = function(self, targets)
	targets = sgs.QList2Table(targets)
	self:sort(targets, "defense")
	for _, p in ipairs(targets)do
		if self:isFriend(p) then
		    return p 
		end
	end
end

sgs.ai_skill_invoke.keqijishan = function(self, data)
	local to = data:toPlayer()
	if self:isFriend(to) and not self:isWeak()
	or self.player:objectName() == to:objectName() then
		return true
	end
end

sgs.ai_use_revises.keqizhenqiao = function(self,card,use)
	if card:isKindOf("Weapon") then
		for _, p in ipairs(self.enemies)do
			if self.player:inMyAttackRange(p) then
				return false
			end
		end
		if #self.enemies<1 then
			return false
		end
	end
end


--孙坚

sgs.ai_skill_discard.keqijuelietwo = function(self, discard_num, min_num, optional, include_equip) 
	local slashone = self.room:getTag("keqijuelietwoFrom"):toPlayer()
	local to_discard = {}
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	self:sortByKeepValue(cards)
	if self:isEnemy(slashone) and (self.player:getCardCount() > 0) then
	    table.insert(to_discard, cards[1]:getEffectiveId())
		if (self:getOverflow() > 1) then
			table.insert(to_discard, cards[2]:getEffectiveId())
		end
		return to_discard
	else
	    return self:askForDiscard("dummyreason", discard_num, discard_num, true, true)
	end
end

sgs.ai_skill_discard.keqipingtao = function(self, discard_num, min_num, optional, include_equip) 
	local to_discard = {}
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	self:sortByKeepValue(cards)
	local room = self.player:getRoom()
	local sj = self.room:getTag("keqipingtaoFrom"):toPlayer()
	if self:isFriend(sj) then
	    table.insert(to_discard, cards[#cards]:getEffectiveId())
		return to_discard
	elseif self:isWeak() 
	and (self.player:getCardCount() > 0)
	and (self:getCardsNum("Peach") + self:getCardsNum("Analeptic") == 0) then
	    table.insert(to_discard, cards[1]:getEffectiveId())
		return to_discard
	else
		return self:askForDiscard("dummyreason", discard_num, discard_num, true, true)
	end
end

local keqipingtao_skill = {}
keqipingtao_skill.name = "keqipingtao"
table.insert(sgs.ai_skills, keqipingtao_skill)
keqipingtao_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("keqipingtaoCard") then return end
	return sgs.Card_Parse("#keqipingtaoCard:.:")
end

sgs.ai_skill_use_func["#keqipingtaoCard"] = function(card, use, self)
	self:sort(self.enemies)
	local enys = sgs.SPlayerList()
	for _, enemy in ipairs(sgs.reverse(self.enemies)) do
		if enys:isEmpty() then
			enys:append(enemy)
		else
			local yes = 1
			for _,p in sgs.qlist(enys) do
				if (enemy:getHp()+enemy:getHp()+enemy:getHandcardNum()) >= (p:getHp()+p:getHp()+p:getHandcardNum()) then
					yes = 0
				end
			end
			if (yes == 1) then
				enys:removeOne(enys:at(0))
				enys:append(enemy)
			end
		end
	end
	for _,enemy in sgs.qlist(enys) do
		if self:objectiveLevel(enemy) > 0 then
			use.card = card
			if use.to then use.to:append(enemy) end
			return
		end
	end
end

sgs.ai_use_value.keqipingtaoCard = 8.5
sgs.ai_use_priority.keqipingtaoCard = 9.5
sgs.ai_card_intention.keqipingtaoCard = 80


--董白
sgs.ai_skill_invoke.keqishichong = function(self, data)
	local to = data:toPlayer()
	return not self:isFriend(to) 
	or to:getCardCount()>4
end

sgs.ai_skill_discard.keqishichong = function(self, discard_num, min_num, optional, include_equip) 
	local db = self.room:getTag("keqishichongFrom"):toPlayer()
	local to_discard = {}
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	self:sortByKeepValue(cards)
	if self:isFriend(db) and (self.player:getCardCount() > 0) then
	    table.insert(to_discard, cards[1]:getEffectiveId())
		return to_discard
	else
	    return self:askForDiscard("dummyreason", discard_num, discard_num, true, true)
	end
end

local keqilianzhu = {}
keqilianzhu.name = "keqilianzhu"
table.insert(sgs.ai_skills, keqilianzhu)
keqilianzhu.getTurnUseCard = function(self)
	if self.player:hasUsed("#keqilianzhuCard") then return end
	return sgs.Card_Parse("#keqilianzhuCard:.:")
end

sgs.ai_skill_use_func["#keqilianzhuCard"] = function(card, use, self)
	local cards = self.player:getCards("h")
	cards = sgs.QList2Table(cards)
	if #cards<2 then return end
	self:sortByKeepValue(cards)
	self:sort(self.enemies,nil,true)
	local ks = {}
	for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if self:isEnemy(p) and p:getCardCount()>0 then
			ks[p:getKingdom()] = (ks[p:getKingdom()] or 0)+1
		end
	end
	local x = 0
	for k,n in pairs(ks)do
		if n>x then x = n end
	end
	for k,n in pairs(ks)do
		if n>=x then
			for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
				if p:getKingdom() == k and self:isFriend(p) and p:getCardCount()>0 then
					use.card = sgs.Card_Parse("#keqilianzhuCard:"..cards[1]:getId()..":")
					use.to:append(p)
					return
				end
			end
			for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
				if p:getKingdom() == k and not self:isEnemy(p) and p:getCardCount()>0 then
					use.card = sgs.Card_Parse("#keqilianzhuCard:"..cards[1]:getId()..":")
					use.to:append(p)
					return
				end
			end
		end
	end
	for k, n in pairs(ks) do
		if n>=x then
			for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
				if p:getKingdom() == k then
					use.card = sgs.Card_Parse("#keqilianzhuCard:"..cards[1]:getId()..":")
					use.to:append(p)
					return
				end
			end
		end
	end
end

sgs.ai_use_value.keqilianzhuCard = 8.5
sgs.ai_use_priority.keqilianzhuCard = 6.5
sgs.ai_card_intention.keqilianzhuCard = 33


--何进

sgs.ai_skill_playerschosen.keqizhaobing = function(self, targets, max, min)
    local selected = sgs.SPlayerList()
    local n = max
    local can_choose = sgs.QList2Table(targets)
    self:sort(can_choose, "defense")
	--优先敌人
    for _,target in ipairs(can_choose) do
        if self:isEnemy(target) and not selected:contains(target) then
            selected:append(target)
            n = n - 1
        end
        if n <= 0 then break end
    end
    return selected
end

sgs.ai_skill_discard.keqizhaobing = function(self, discard_num, min_num, optional, include_equip) 
	local to_discard = {}
	local room = self.player:getRoom()
	local hj = room:getCurrent()
	local cards = self.player:getCards("he")
	for _,c in sgs.qlist(cards) do 
		if not c:isKindOf("Slash") then
			cards:removeOne(c)
		end
	end
	cards = sgs.QList2Table(cards)
	self:sortByKeepValue(cards)
	if self.player:isFriend(hj) and (cards:length() > 0) then
	    table.insert(to_discard, cards[1]:getEffectiveId())
		return to_discard
	else
	    return self:askForDiscard("dummyreason", discard_num, discard_num, true, true)
	end
end



sgs.ai_skill_invoke.keqizhaobing = function(self, data)
	if self.player:getHandcardNum() < 3 then
	    return true
	end
end

sgs.ai_skill_invoke.keqizhuhuan = function(self, data)
	return true
end

sgs.ai_skill_playerchosen.keqizhuhuan = function(self, targets)
	targets = sgs.QList2Table(targets)
	local theweak = sgs.SPlayerList()
	local theweaktwo = sgs.SPlayerList()
	local myfriends = sgs.SPlayerList() 
	for _, p in ipairs(targets) do
		if self:isEnemy(p) then
			theweak:append(p)
		end
	end
	for _, p in ipairs(targets) do
		myfriends:append(p)
	end
	for _,qq in sgs.qlist(theweak) do
		if theweaktwo:isEmpty() then
			theweaktwo:append(qq)
		else
			local inin = 1
			for _,pp in sgs.qlist(theweaktwo) do
				if (pp:getHp() < qq:getHp()) then
					inin = 0
				end
			end
			if (inin == 1) then
				theweaktwo:append(qq)
			end
		end
	end
	if self:isWeak() then
		return myfriends:at(0)
	elseif theweaktwo:length() > 0 then
	    return theweaktwo:at(0)
	end
	return nil
end

sgs.ai_skill_choice.keqizhuhuan = function(self, choices, data)
	local room = self.player:getRoom()
	local items = choices:split("+")
	local hj = room:getCurrent()
	if (self:isWeak() or self:isFriend(hj)) or (self:isEnemy(hj) and not hj:isWounded()) then
		return items[2]
	else
		return items[1]
	end
end

--皇甫嵩
local keqiguanhuo_skill = {}
keqiguanhuo_skill.name = "keqiguanhuo"
table.insert(sgs.ai_skills, keqiguanhuo_skill)
keqiguanhuo_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("keqiguanhuoCard") and (self.player:getMark("aiguanhuo-PlayClear") == 0) then return end
	local fa = sgs.Sanguosha:cloneCard("fire_attack")
	fa:setSkillName("keqiguanhuo")
	fa:setFlags("keqiguanhuo")
	fa:deleteLater()
	if self.player:getMark("usekeqiguanhuo-PlayClear")>0 then
		local d = self:aiUseCard(fa)
		if d.card then
			local cards = self.player:getCards("h")
			for _,p in sgs.qlist(d.to) do
				local n = 0
				for _,c in sgs.list(cards) do
					if #getKnownCards(p,self.player,"h",c:getSuit())>0 then
						n = n+1
					end
				end
				if n>cards:length()/2 then
					return fa
				end
			end
		end
	else
		return fa
	end
--	return sgs.Card_Parse("#keqiguanhuoCard:.:")
end

--原版索敌
--[[
sgs.ai_skill_use_func["#keqiguanhuoCard"] = function(card, use, self)
    if (not self.player:hasUsed("#keqiguanhuoCard")) or (self.player:getMark("aiguanhuo-PlayClear") > 0) then
        self:sort(self.enemies)
	    self.enemies = sgs.reverse(self.enemies)
		for _, enemy in ipairs(self.enemies) do
		    if self:objectiveLevel(enemy) > 0 then
			    use.card = card
			    if use.to then use.to:append(enemy) end
		        return
			end
		end
	end
end
]]
sgs.ai_skill_use_func["#keqiguanhuoCard"] = function(card, use, self)
    if (not self.player:hasUsed("#keqiguanhuoCard")) or (self.player:getMark("aiguanhuo-PlayClear") > 0) then
        self:sort(self.enemies)
	    self.enemies = sgs.reverse(self.enemies)
		local enys = sgs.SPlayerList()
		for _, enemy in ipairs(self.enemies) do
			if not enemy:isKongcheng() then
				if enys:isEmpty() then
					enys:append(enemy)
				else
					local yes = 1
					for _,p in sgs.qlist(enys) do
						if (enemy:getHp()+enemy:getHp()+enemy:getHandcardNum()) >= (p:getHp()+p:getHp()+p:getHandcardNum()) then
							yes = 0
						end
					end
					if (yes == 1) then
						enys:removeOne(enys:at(0))
						enys:append(enemy)
					end
				end
			end
		end
		for _,enemy in sgs.qlist(enys) do
			if self:objectiveLevel(enemy) > 0 then
			    use.card = card
			    if use.to then use.to:append(enemy) end
		        return
			end
		end
	end
end
sgs.ai_use_value.keqiguanhuoCard = 8.5
sgs.ai_use_priority.keqiguanhuoCard = 9.5
sgs.ai_card_intention.keqiguanhuoCard = 80

sgs.ai_skill_invoke.keqijuxia = function(self, data)
	local to = data:toPlayer()
	local use = self.room:getTag("keqijuxiaData"):toCardUse()
	return self:isFriend(to) and (use.card:isDamageCard() or use.to:length()>1)
end


--孔融

sgs.ai_skill_invoke.keqimingshi = function(self, data)
	local to = data:toPlayer()
	if self:isFriend(to) and not self:isWeak() then
	    return true
	end
end

sgs.ai_skill_invoke.keqilirang_use = function(self, data)
	if self.player:hasFlag("aiuselirang") then
	    return true
	end
end
sgs.ai_skill_discard.keqilirang = function(self) --给牌
	local to = self.player:getTag("keqilirangTo"):toPlayer()
	local to_discard = {}
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	if (self:isFriend(to) or not self:isEnemy(to) and #self.friends<#self.enemies)
	and (not self:isWeak() or #cards>3) then
		self:sortByKeepValue(cards)
		for _, c in sgs.list(cards) do
			if c:isAvailable(to) then
				table.insert(to_discard, c:getEffectiveId())
				if #to_discard>1 then break end
			end
		end
		for _, c in sgs.list(cards) do
			if #to_discard>1 then break end
			if #to_discard>0 and not table.contains(to_discard, c:getEffectiveId()) then
				table.insert(to_discard, c:getEffectiveId())
			end
		end
	end
	return to_discard
end
sgs.ai_skill_invoke.keqilirang_get = function(self, data)
	return true
end
--刘宏
sgs.ai_skill_invoke.keqichaozheng = function(self, data)
	for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if self:isFriend(p) then
			return true
		end
	end
	return false
end

local keqishenchong_skill = {}
keqishenchong_skill.name = "keqishenchong"
table.insert(sgs.ai_skills, keqishenchong_skill)
keqishenchong_skill.getTurnUseCard = function(self)
	if self.player:getMark("@keqishenchong") == 0 then return end
	return sgs.Card_Parse("#keqishenchongCard:.:")
end

sgs.ai_skill_use_func["#keqishenchongCard"] = function(card, use, self)
	self:sort(self.friends,"defense",true)
	for _, fri in ipairs(self.friends) do
		if (fri:objectName() ~= self.player:objectName()) then
			use.card = card
			if use.to then use.to:append(fri) end
			return
		end
	end
end

sgs.ai_use_value.keqiguanhuoCard = 8.5
sgs.ai_use_priority.keqiguanhuoCard = 9.5
sgs.ai_card_intention.keqiguanhuoCard = 80

local keqitongjue = {}
keqitongjue.name = "keqitongjue"
table.insert(sgs.ai_skills, keqitongjue)
keqitongjue.getTurnUseCard = function(self)
	for i,uc in ipairs(self.toUse)do
		if i>1 then
			if uc:isKindOf("AOE") or uc:isKindOf("GlobalEffect") then
				local ids = {}
				for _, c in sgs.qlist(self.player:getCards("h")) do
					if c:isKindOf("BasicCard")
					and c:getEffectiveId()~=uc:getEffectiveId() then
						table.insert(ids, c:getEffectiveId())
					end
				end
				if #ids<1 then continue end
				return sgs.Card_Parse("#keqitongjueCard:"..table.concat(ids,"+")..":")
			else
				local d = self:aiUseCard(uc)
				if d.card and d.to:length()>1 then
					local ids = {}
					for _, c in sgs.qlist(self.player:getCards("h")) do
						if c:isKindOf("BasicCard")
						and c:getEffectiveId()~=uc:getEffectiveId() then
							table.insert(ids, c:getEffectiveId())
						end
					end
					if #ids<1 then continue end
					return sgs.Card_Parse("#keqitongjueCard:"..table.concat(ids,"+")..":")
				end
			end
		end
	end
end

sgs.ai_skill_use_func["#keqitongjueCard"] = function(card, use, self)
	self:sort(self.friends_noself,"defense")
	for _, fri in ipairs(self.friends_noself) do
		if fri:getKingdom()=="qun" then
			use.card = card
			if use.to then use.to:append(fri) end
			break
		end
	end
end

sgs.ai_use_value.keqitongjueCard = 8.5
sgs.ai_use_priority.keqitongjueCard = 9.5
sgs.ai_card_intention.keqitongjueCard = -80


--[[sgs.ai_skill_cardchosen.keqichaozheng_yishi = function(self,who)
	local player = self.player
	for _,c in sgs.qlist(who:getCards("h")) do
		if c:hasFlag("chaozhengred") or c:hasFlag("chaozhengblack") then
			return c:getId()
		end
	end
	return -1
end]]

sgs.ai_skill_discard.keqichaozheng = function(self)
	local to_discard = {}
	local from = self.room:getCurrent()
	local n = 0
	for _,p in sgs.qlist(self.room:getOtherPlayers(from)) do
		if p:isWounded() then
			n = n+1
			if not self:isEnemy() then
				n = n+1
			end
		else
			n = n-1
		end
	end
	for _,c in sgs.qlist(self.player:getCards("h")) do
		if n >1 and c:isRed() then
			table.insert(to_discard, c:getEffectiveId())
			break
		end
	end
	for _,c in sgs.qlist(self.player:getCards("h")) do
		if #to_discard<1 and not c:isRed() then
			table.insert(to_discard, c:getEffectiveId())
			break
		end
	end
	if #to_discard<1 then
		for i,c in sgs.qlist(self.player:getCards("h")) do
			table.insert(to_discard, c:getEffectiveId())
			break
		end
	end
	return to_discard
end

sgs.ai_skill_invoke.keqijulian = function(self,data)
    return true
end

sgs.ai_skill_invoke.keqitushe = function(self,data)
	for _, c in sgs.qlist(self.player:getCards("h")) do
		if c:isKindOf("BasicCard") then return false end
	end
    return true
end

--南华老仙
sgs.ai_armor_value._keqi_taipingyaoshu = 6
sgs.ai_ajustdamage_to._keqi_taipingyaoshu = function(self,from,to,card,nature)
	if nature~="N" then return -99 end
end

sgs.ai_skill_playerchosen.keqishoushu = function(self, targets)
	targets = sgs.QList2Table(targets)
	for _, p in ipairs(targets) do
		if (self.player:objectName() == p:objectName()) then
		    return p 
		end
	end
	return nil
end

sgs.ai_skill_playerchosen.keqishoushutwo = function(self, targets)
	targets = sgs.QList2Table(targets)
	for _, p in ipairs(targets) do
		if (self.player:objectName() == p:objectName()) then
		    return p 
		end
	end
	return nil
end

sgs.ai_skill_playerschosen.keqiwendao = function(self, targets,x,n)
	targets = sgs.QList2Table(targets)
	self:sort(targets,"defense",true)
	local tos = {}
	local judge = self.room:getTag("keqiwendaoJudge"):toJudge()
	if not judge:isGood() then
		for _, p in ipairs(targets) do
			if self:isFriend(p) and #tos<1 then
				for _, c in ipairs(getKnownCards(p,self.player,"he")) do
					if p:isJilei(c) then continue end
					if judge:isGood(c) then
						table.insert(tos, p)
						break
					end
				end
			end
		end
		for _, p in ipairs(targets) do
			if #tos==1 and self:isEnemy(p) then
				table.insert(tos, p)
			end
		end
		for _, p in ipairs(targets) do
			if #tos==1 and not self:isFriend(p) then
				table.insert(tos, p)
			end
		end
		for _, p in ipairs(targets) do
			if #tos==1 and tos[1]~=p then
				table.insert(tos, p)
			end
		end
	end
	return tos
end

sgs.ai_skill_discard.keqiwendao = function(self)
	local judge = self.room:getTag("keqiwendaoJudge"):toJudge()
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	self:sortByKeepValue(cards)
	if self:isFriend(judge.who) then
		for _, c in ipairs(cards) do
			if self.player:isJilei(c) then continue end
			if judge:isGood(c) then
				return {c:getEffectiveId()}
			end
		end
	else
		for _, c in ipairs(cards) do
			if self.player:isJilei(c) then continue end
			return {c:getEffectiveId()}
		end
	end
end

sgs.ai_skill_askforag.keqiwendao = function(self,card_ids)
	local judge = self.room:getTag("keqiwendaoJudge"):toJudge()
	for _,id in sgs.list(card_ids) do
		if judge:isGood(sgs.Sanguosha:getCard(id)) then
			return id
		end
	end
end

sgs.ai_skill_invoke.keqixuanhua = function(self,data)
	if self.player:getPhase() == sgs.Player_Start then
		return not self:damageIsEffective(self.player,"T")
		or self:getFinalRetrial(nil,"lightning")<2
	end
	return (not self:damageIsEffective(self.player,"T") or self:getFinalRetrial(nil,"lightning")==1)
	and #self.enemies>0
end

sgs.ai_skill_playerchosen.keqixuanhua = function(self, targets)
	targets = sgs.QList2Table(targets)
	for _, p in ipairs(targets) do
		if self.player:getPhase() == sgs.Player_Start and self:isFriend(p) and p:isWounded() then
		    return p 
		end
	end
	for _, p in ipairs(targets) do
		if self.player:getPhase() == sgs.Player_Finish and self:isEnemy(p) then
		    return p 
		end
	end
end

--桥玄
sgs.ai_skill_invoke.keqijuezhi_wq = function(self,data)
    return true
end
sgs.ai_skill_invoke.keqijuezhi_fj = function(self,data)
    return true
end
sgs.ai_skill_invoke.keqijuezhi_fy = function(self,data)
    return true
end
sgs.ai_skill_invoke.keqijuezhi_jg = function(self,data)
    return true
end
sgs.ai_skill_invoke.keqijuezhi_bw = function(self,data)
    return true
end
sgs.ai_skill_invoke.keqijuezhi = function(self,data)
    return true
end
sgs.ai_skill_playerchosen.keqijizhao = function(self, targets)
	targets = sgs.QList2Table(targets)
	self:sort(targets,nil,true)
	for _, p in ipairs(targets) do
		if self:isFriend(p) and p:getHandcardNum()>2 then
		    return p 
		end
	end
	for _, p in ipairs(targets) do
		if p:hasEquip() and self:doDisCard(p,"e",true) then
		    return p 
		end
	end
	return nil
end



--王允

local keqishelun_skill = {}
keqishelun_skill.name = "keqishelun"
table.insert(sgs.ai_skills, keqishelun_skill)
keqishelun_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("keqishelunCard") then return end
	return sgs.Card_Parse("#keqishelunCard:.:")
end

sgs.ai_skill_use_func["#keqishelunCard"] = function(card, use, self)
    if not self.player:hasUsed("#keqishelunCard") then
        self:sort(self.enemies)
	    self.enemies = sgs.reverse(self.enemies)
		local enys = sgs.SPlayerList()
		for _, enemy in ipairs(self.enemies) do
			if self.player:inMyAttackRange(enemy) then
				if enys:isEmpty() then
					enys:append(enemy)
				else
					local yes = 1
					for _,p in sgs.qlist(enys) do
						if (enemy:getHp()+enemy:getHp()+enemy:getHandcardNum()) >= (p:getHp()+p:getHp()+p:getHandcardNum()) then
							yes = 0
						end
					end
					if (yes == 1) then
						enys:removeOne(enys:at(0))
						enys:append(enemy)
					end
				end
			end
		end
		if (enys:length() > 0) then
			for _,enemy in sgs.qlist(enys) do
				if self:objectiveLevel(enemy) > 0 then
					use.card = card
					if use.to then use.to:append(enemy) end
					return
				end
			end
		end
	end
end

sgs.ai_use_value.keqishelunCard = 8.5
sgs.ai_use_priority.keqishelunCard = 9.5
sgs.ai_card_intention.keqishelunCard = 80

sgs.ai_skill_playerchosen.keqifayi = function(self, targets)
	targets = sgs.QList2Table(targets)
	self:sort(targets)
	for _, p in ipairs(targets) do
		if self:isEnemy(p) then
		    return p 
		end
	end
end

sgs.ai_skill_discard.keqishelun = function(self)
	local to = self.room:getTag("keqishelunTo"):toPlayer()
	local cards = self.player:getCards("h")
	cards = sgs.QList2Table(cards)
	self:sortByKeepValue(cards)
	if self:isFriend(to) or not self:isEnemy(to) and #self.enemies>#self.friends then
		for _,c in sgs.list(cards) do
			if c:isRed() then
				return {c:getEffectiveId()}
			end
		end
	end
	return {cards[1]:getEffectiveId()}
end


local keqipingjian = {}
keqipingjian.name = "keqipingjian"
table.insert(sgs.ai_skills, keqipingjian)
keqipingjian.getTurnUseCard = function(self,ex)
	for _,s in sgs.list(qiPingSkills(self.player))do
		if s:inherits("FilterSkill") then continue end
		if s:inherits("ViewAsSkill")
		and sgs.Sanguosha:getViewAsSkill(s:objectName()):isEnabledAtPlay(self.player) then
			local sk = sgs.ai_fill_skill[s:objectName()]
			if sk then
				sk = sk(self,ex)
				if sk then
					local d = self:aiUseCard(sk)
					if d.card then
						self.keqipingjianUse = d
						sgs.ai_skill_choice.keqipingjian = s:objectName()
						sgs.ai_use_priority.keqipingjianCard = sgs.ai_use_priority[sk:getClassName()]
						return sgs.Card_Parse("#keqipingjianCard:.:")
					end
				end
			end
		elseif s:inherits("TriggerSkill") then
			local ts = sgs.Sanguosha:getTriggerSkill(s:objectName())
			ts = ts:getViewAsSkill()
			if ts and ts:isEnabledAtPlay(self.player) then
				local sk = sgs.ai_fill_skill[ts:objectName()]
				if sk then
					sk = sk(self,ex)
					if sk then
						local d = self:aiUseCard(sk)
						if d.card then
							self.keqipingjianUse = d
							sgs.ai_skill_choice.keqipingjian = ts:objectName()
							sgs.ai_use_priority.keqipingjianCard = sgs.ai_use_priority[sk:getClassName()]
							return sgs.Card_Parse("#keqipingjianCard:.:")
						end
					end
				end
			end
		end
	end
end

sgs.ai_skill_use_func["#keqipingjianCard"] = function(card, use, self)
	use.card = card
end

sgs.ai_use_value.keqipingjianCard = 8.5
sgs.ai_use_priority.keqipingjianCard = 9.5

sgs.ai_skill_use["@@keqipingjian"] = function(self,prompt)
    local dummy = self.keqipingjianUse
   	if dummy.card then
      	local tos = {}
       	for _,p in sgs.list(dummy.to)do
       		table.insert(tos,p:objectName())
       	end
       	return dummy.card:toString().."->"..table.concat(tos,"+")
    end
end

--杨彪
local keqiyizheng_skill = {}
keqiyizheng_skill.name = "keqiyizheng"
table.insert(sgs.ai_skills, keqiyizheng_skill)
keqiyizheng_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("keqiyizhengCard") then return end
	return sgs.Card_Parse("#keqiyizhengCard:.:")
end

sgs.ai_skill_use_func["#keqiyizhengCard"] = function(card, use, self)
    self:sort(self.enemies,nil,true)
	local mc = self:getMaxCard()
	if not mc or mc:getNumber()<11 then return end
	for _, enemy in ipairs(self.enemies) do
		if self.player:getHandcardNum()<enemy:getHandcardNum() then
			use.card = card
		    if use.to then use.to:append(enemy) end
		    break
		end
	end
end

sgs.ai_use_value.keqiyizhengCard = 8.5
sgs.ai_use_priority.keqiyizhengCard = 3.5
sgs.ai_card_intention.keqiyizhengCard = 80

sgs.ai_skill_choice.keqiyizheng = function(self,choices,data)
	local from = data:toPlayer()
	local items = choices:split("+")
	if self:isFriend(from) then
		if self:isWeak(from) then
			return items[1]
		end
	elseif self:isEnemy(from) then
		if self:isWeak(from) then
			return items[3]
		end
	end
	return items[2]
end

sgs.ai_skill_invoke.keqirangjie = function(self,data)
    return sgs.ai_skill_invoke.peiqi(self,data)
end

sgs.ai_skill_playerchosen["keqirangjie_from"] = function(self,players)
	for _,target in sgs.list(players)do
		if target:objectName()==self.peiqiData.from:objectName()
		then return target end
	end
end

sgs.ai_skill_playerchosen["keqirangjie_to"] = function(self,players)
	for _,target in sgs.list(players)do
		if target:objectName()==self.peiqiData.to:objectName()
		then return target end
	end
end

sgs.ai_skill_cardchosen.keqirangjie = function(self,who,flags,method)
	for _,e in sgs.list(who:getCards(flags))do
		local id = e:getEffectiveId()
		if id==self.peiqiData.cid
		then return id end
	end
end

--朱儁
sgs.ai_skill_invoke.keqifendi = function(self,data)
    return true
end

sgs.ai_skill_invoke.keqijuxiang = function(self,data)
	local cp = self.room:getCurrent()
	local move = data:toMoveOneTime()
    if self:isFriend(cp) and cp:getPhase()<=sgs.Player_Play
	and not self.player:hasFlag("keqijuxiang") and move.card_ids:length()<=2 then
		self.player:setFlags("keqijuxiang")
		return #self.enemies>0
	end
end

sgs.ai_skill_cardchosen.keqifendi = function(self,who,flags,method)
	if self.disabled_ids.length()>=who:getHandcardNum()/2
	or self.disabled_ids.length()>=math.random(1,who:getHandcardNum())
	then return -1 end
end

--王荣
sgs.ai_skill_invoke.keqijizhanw = function(self,data)
    return true
end

sgs.ai_skill_choice.keqijizhanw = function(self,choices,data)
	local n = data:toInt()
	local player = self.player
	local items = choices:split("+")
	if n>6 then return items[2] end
	if n<7 then return items[1] end
	return items[2]
end


sgs.ai_skill_playerchosen.keqifusong = function(self,players)
	local player = self.player
	players = self:sort(players,"card",true)
    for _,target in sgs.list(players)do
		if self:isFriend(target)
		then return target end
	end
    for _,target in sgs.list(players)do
		if not self:isEnemy(target)
		then return target end
	end
end









