﻿/********************************************************************
    Copyright (c) 2013-2014-QSanguosha-Rara

    This file is part of QSanguosha-Hegemony.

    This game is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License as
    published by the Free Software Foundation; either version 3.0
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
    General Public License for more details.

    See the LICENSE file for more details.

    QSanguosha-Rara
    *********************************************************************/

import QtQuick 1.0

Item {
    Rectangle {
        id:mask
        x:0
        width:sceneWidth
        height:180
        y:sceneHeight*0.6
        color:"black"
        opacity:0
        z:-1
    }
	Image {
		id:heroPic
		x:2333
		y:sceneHeight*0.4
		fillMode:Image.PreserveAspectFit
        source:"../image/fullskin/generals/full/"+hero+".jpg"
		opacity:0.6
		scale:0.8
		z:991
	}
    Rectangle {
        id:heroPicBg
        color:"transparent"
 		x:544
		y:sceneHeight*0.4-88
		opacity:0
        z:991
        Image {
            fillMode:Image.PreserveAspectFit
            source:"../image/fullskin/generals/full/"+hero+".jpg"
            scale:1.7
        }
        Image {
			x:-360
			y:sceneHeight*0.4-510
            fillMode:Image.PreserveAspectFit
            source:"../image/fullskin/generals/bluemask.png"
			opacity:0.4
			scale:1.8
        }
        Image {
			x:-103
			y:sceneHeight*0.4-470
            fillMode:Image.PreserveAspectFit
            source:"../image/fullskin/generals/broadcast.png"
            scale:1.7
        }
    }
	
    FontLoader {
        id:bwk
        source:"../font/wenq.ttf"
    }
	
    Text {
        id:text
        color:"white"
        text:skill
        font.family:"隶书"
        style:Text.Outline
        font.pointSize:900
        opacity:0
        z:999
        x:sceneWidth/2-66
        y:sceneHeight/2+45
    }
	
    ParallelAnimation {
        id:step1
        running:false
        PropertyAnimation {
            target:heroPic
            property:"x"
            to:sceneWidth/2-500
            duration:800
            easing.type:Easing.OutQuad
			easing.overshoot:3
        }
        PropertyAnimation{
            target:mask
            property:"opacity"
            to:0.6
            duration:0
        }
        onCompleted:{
			step2.start()
        }
    }
	
    SequentialAnimation {
        id:step2
        onCompleted:{
            container.visible = false
            container.animationCompleted()
        }
		
        ParallelAnimation {
			PropertyAnimation {
                target:heroPicBg
                property:"opacity"
                to:1
                duration:800
				easing.overshoot:3
				easing.type:Easing.OutQuad
            }
            PropertyAnimation {
                target:text
                property:"opacity"
                to:1.0
                duration:800
            }
            PropertyAnimation {
                target:text
                property:"font.pointSize"
                to:90
                duration:800
            }
        }
		
        PauseAnimation { duration:2500 }
		
    }

    Component.onCompleted:{
        step1.start()
    }
}

/*
Item {
    Rectangle {
        id:mask
        height:sceneHeight
        width:sceneWidth
        color:"black"
        opacity:0
    }
    
    Image {
        id:heroPic
        x:sceneWidth/2-width/2
        y:sceneHeight/2-height/2
        fillMode:Image.PreserveAspectFit
        source:"../image/fullskin/generals/full/"+hero+".png"
        scale:0.4
        rotation:-10
        z:998
    }
    
    FontLoader {
        id:bwk
        source:"../font/FZBWKSK.TTF"
    }
    
    Text {
        id:text
        color:"white"
        text:skill
        font.family:bwk.name
        style:Text.Outline
        font.pointSize:90
        opacity:0
        z:999
        x:sceneWidth/2
        y:sceneHeight/2+45
    }
    
    ParallelAnimation {
        id:step1
        running:false
        PropertyAnimation {
            target:heroPic
            property:"scale"
            to:2.2
            duration:400
            easing.type:Easing.OutQuad
			easing.overshoot:3
        }
        PropertyAnimation{
            target:mask
            property:"opacity"
            to:0.4
            duration:400
        }
        onCompleted:{
			step2.start()
        }
    }
	
    SequentialAnimation {
        id:step2
        onCompleted:{
            container.visible = false
            container.animationCompleted()
        }
        
        PauseAnimation {
            duration:100
        }
        
        ParallelAnimation {
            PropertyAnimation {
                target:heroPic
                property:"scale"
                to:1
                duration:500
                easing.type:Easing.InQuad
            }
            PropertyAnimation {
                target:heroPic
                property:"x"
                to:sceneWidth/2-heroPic.width/2-150
                duration:500
                easing.type:Easing.InQuad
            }
            SequentialAnimation {
                PauseAnimation {
                    duration:240
                }
                ParallelAnimation {
                    PropertyAnimation {
                        target:text
                        property:"opacity"
                        to:1.0
                        duration:800
                    }
                    PropertyAnimation {
                        target:text
                        property:"x"
                        to:sceneWidth/2+15
                        duration:800
                    }
                }
            }
        }
		
        PauseAnimation { duration:2400 }
		
    }

    Component.onCompleted:{
        step1.start()
    }
}
*/
