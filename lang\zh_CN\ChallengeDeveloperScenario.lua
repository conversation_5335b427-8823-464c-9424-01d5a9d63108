-- translation for ChallengeDeveloper Scenario

return {
	["challengedeveloper"] = "挑战开发组",
	
	["ChallengeDeveloperRule:change"] = "你是否变为女性？",
	["ChallengeDeveloperRuleSkill"] = "请获得一个技能",
	["#mengxindekunhuo1"]="image=image/animate/mengxindekunhuo1.png",
	["#mengxindekunhuo2"]="image=image/animate/mengxindekunhuo2.png",
	["#dashendenuhuo"]="image=image/animate/dashendenuhuo.png",
	
	["dev_xusine"] = "Xusineday",
	["&dev_xusine"] = "数字君",
	["designer:dev_xusine"] = "Xusineday",
	["dev_lvedong"] = "略懂",
	[":dev_lvedong"] = "当你需要使用或打出基本牌时，你可展示牌堆顶2+X张牌（X为你已损失的体力值），然后使用或打出其中的此牌。这些牌中，你可以将不为此牌的黑桃牌当【酒】，"..
						"以下同理：方块当【杀】，梅花牌当【闪】。",
	["dev_lvedong_slash"] = "略懂",
	["dev_lvedong_saveself"] = "略懂",
	["dev_caiyi"] = "猜疑",
	[":dev_caiyi"] = "当你使用【杀】指定一名角色为目标后，你可令其选择一项：弃置一张【杀】；或不能响应此【杀】。",
	["@dev_caiyi-discard"] = "请弃置一张【杀】，否则你不能响应此【杀】",
	
	["dev_duguanhe"] = "独孤安河",
	["designer:dev_duguanhe"] = "独孤安河＆Xusineday",
	["dev_jianshi"] = "箭矢",
	[":dev_jianshi"] = "当你因弃置而失去黑色牌后，你可将在弃牌堆中的这些牌置于你的武将牌上称为“箭”。回合外，你可将两张“箭”当【无懈可击】使用。",
	["dev_jian"] = "箭",
	["dev_cangdao"] = "藏刀",
	[":dev_cangdao"] = "当你的一张装备牌进入弃牌堆前，你可将其交给一名角色，然后你可视为对其使用一张【杀】（不计次）。",
	["@dev_cangdao-give"] = "你可将一张装备牌交给一名角色",
	
	["dev_chongmei"] = "虫妹",
	["designer:dev_chongmei"] = "虫妹＆Xusineday",
	["dev_pianxian"] = "翩跹",
	[":dev_pianxian"] = "锁定技，游戏开始时，你获得三枚“蝶”标记。当你于回合外获得或失去牌后，你获得等量的“蝶”标记。",
	["dev_die"] = "蝶",
	["dev_quanneng"] = "全能",
	[":dev_quanneng"] = "你可以弃置一枚“蝶”标记来发动以下技能：“枭姬”，“界集智”，“补益”，“淑慎”，“烈刃”。",
	["dev_quanneng:xiaoji"] = "你是否发动“全能（枭姬）”？",
	["dev_quanneng:tenyearjizhi"] = "你是否发动“全能（界集智）”？",
	["dev_quanneng_buyi"] = "全能（补益）",
	["dev_quanneng:shushen"] = "你是否发动“全能（淑慎）”？",
	["dev_quanneng_lieren"] = "全能（烈刃）？",
	
	["dev_dudu"] = "嘟嘟",
	["designer:dev_dudu"] = "嘟嘟＆Xusineday",
	["dev_pofeng"] = "破风",
	[":dev_pofeng"] = "出牌阶段限一次，你可摸一张牌并弃置一张牌，然后令其他角色各摸一张牌并弃置一张牌，以此法弃置牌点数大于你的角色回复1点体力，点数小于你的角色失去1点体力。",
	["@PofengAsk"] = "请弃置一张牌来响应“破风”（发动者的点数是%src）",
	["dev_xiaohun"] = "销魂",
	[":dev_xiaohun"] = "限定技，出牌阶段，你可将任意数量的手牌正面朝上交给相同数量的其他角色。若一名角色以此法获得的牌点数是其体力值的整数倍，你弃置其两张牌并对其造成1点火焰伤害；"..
						"若不为整数倍，该角色摸两张牌。",
	["@dev_xiaohun-give"] = "请分配手牌",
	
	["dev_db"] = "doublebit",
	["&dev_db"] = "DB君",
	["designer:dev_db"] = "doublebit＆Xusineday",
	["dev_saiche"] = "塞车",
	[":dev_saiche"] = "其他角色的回合开始时，你可以交给该角色一张手牌，然后本回合该角色使用牌只能指定距离不大于1的角色为目标。",
	["@dev_saiche-give"] = "你可以发动“塞车”交给 %src 一张手牌",
	["dev_zhuaji"] = "爪机",
	[":dev_zhuaji"] = "锁定技，结束阶段开始时，你按以下规则获得相应技能直到下回合开始（X为你本回合出牌阶段使用的牌数）：若X小于1，你获得技能“120”；若X等于1，你获得技能“119”；"..
					"若X大于1，你获得技能“110”。",
	["dev_110"] = "110",
	[":dev_110"] = "锁定技，当你于回合外第一次受到伤害时，伤害来源将武将牌翻面。",
	["dev_119"] = "119",
	[":dev_119"] = "锁定技，当你于回合外第一次受到属性伤害时，防止此伤害。",
	["dev_120"] = "120",
	[":dev_120"] = "锁定技，回合结束时，你复原武将牌，然后回复1点体力。",
	
	["dev_amira"] = "阿米拉嗪",
	["designer:dev_amira"] = "阿米拉嗪＆Xusineday",
	["dev_jiayao"] = "假药",
	[":dev_jiayao"] = "锁定技，当你于出牌阶段内对一名其他角色造成伤害后，该角色选项一项：弃置一张手牌；或下一个摸牌阶段少摸一张牌。",
	["@dev_jiayao-discard"] = "请弃置一张手牌，否则下个摸牌阶段少摸一张牌",
	["dev_shangyin"] = "上瘾",
	[":dev_shangyin"] = "当一名角色于你的回合内进入濒死状态时，你可令一名角色摸一张牌，然后本回合你可额外使用一张【杀】。",
	["@dev_shangyin-invoke"] = "你可以令一名角色摸一张牌",
	
	--[[["dev_chongmei"] = "虫妹",--3 nv
	["designer:dev_chongmei"] = "叫什么啊你妹",
	["dev_xiwen"] = "喜闻",
	[":dev_xiwen"] = "准备阶段开始时，若弃牌堆内有【桃】，你可以对一名角色使用其中的一张【桃】。",
	["@dev_xiwen-invoke"] = "你可以对一名角色发动“喜闻”",
	["dev_lejian"] = "乐见",
	[":dev_lejian"] = "结束阶段开始时，若你于此回合内使用过【桃】且弃牌堆内有【杀】，你可以对一名其他角色使用其中一张【杀】。",
	["@dev_lejian-invoke"] = "你可以对一名其他角色发动“乐见”",]]
	
	["dev_mye"] = "myetyet",
	["&dev_mye"] = "橙子君",
	["designer:dev_mye"] = "叫什么啊你妹",
	["dev_xiancheng"] = "鲜橙",
	[":dev_xiancheng"] = "结束阶段开始时，你可以获得弃牌堆内的一张【杀】。",
	["dev_chengzhi"] = "橙汁",
	[":dev_chengzhi"] = "当一名角色处于濒死状态时，你可以将一张【杀】正面向上置于牌堆顶，该角色将体力回复至1点。",
	["dev_bancheng"] = "半橙",
	[":dev_bancheng"] = "当你受到1点伤害后或出牌阶段限一次，你可以指定一名其他角色并翻开牌堆顶的X+2张牌（X为你已损失的体力值），你对其造成等同于其中【杀】数量的伤害，"..
						"然后该角色获得其中一张牌并将其余牌置入弃牌堆。",
	["@dev_bancheng"] = "你可以对一名其他角色发动“半橙”",
	
	["dev_yizhiyongheng"] = "医治永恒",
	["designer:dev_yizhiyongheng"] = "叫什么啊你妹",
	["dev_shuguang"] = "曙光",
	[":dev_shuguang"] = "当一名开发组角色进入濒死状态时，若你的体力上限不小于存活角色数，你可以减1点体力上限，然后该角色回复所有体力。",
	
	["dev_yuanjiati"] = "元嘉体",
	["designer:dev_yuanjiati"] = "叫什么啊你妹",
	["dev_baoji"] = "暴击",
	[":dev_baoji"] = "锁定技，当你造成伤害时，你有75%的几率令此伤害+1。",
	["dev_shanbi"] = "闪避",
	[":dev_shanbi"] = "锁定技，当你受到伤害时，你有75%的几率防止此伤害。",
	
	["dev_funima"] = "付尼玛",
	["designer:dev_funima"] = "叫什么啊你妹",
	["dev_nini"] = "妮妮",
	[":dev_nini"]="出牌阶段限一次，你可以用牌堆顶的一张牌与一名角色拼点，若你赢，你获得弃牌堆内你的拼点牌且本回合你的攻击范围+1。",
	["dev_danteng"] = "蛋疼",
	[":dev_danteng"] = "当一名角色于其出牌阶段内造成伤害后，你可以摸一张牌，然后若该角色不为你且未受伤，其结束出牌阶段。",
	
	["dev_para"] = "Paracel_007",
	["&dev_para"] = "para",
	["designer:dev_para"] = "叫什么啊你妹",
	["dev_gengxin"] = "更新",
	[":dev_gengxin"] = "<font color=\"green\"><b>本技能有90秒冷却时间，</b></font>结束阶段开始时，你可以弃置一名角色装备区里的武器牌或将一张武器牌置入一名角色的装备区，然后你获得等同于此牌攻击范围的护甲。",
	["#DevGengxinCD"] = "%from 的“%arg”还有 %arg2 秒冷却时间",
	["@dev_gengxin"] = "你可以发动“更新”",
	["dev_xueba"] = "学霸",
	[":dev_xueba"] = "<font color=\"green\"><b>对每名角色限一次，</b></font>当一名角色进入濒死状态时，你可以对其使用牌堆内的第一张【桃】，然后你发现一个神势力武将的技能。",
	
	["dev_rara"] = "rara",
	["&dev_rara"] = "啦啦",
	["designer:dev_rara"] = "叫什么啊你妹",
	["dev_meihuo"] = "魅惑",
	[":dev_meihuo"] = "当你成为【杀】的目标后，你可以重铸此【杀】并展示手牌中以此法获得的牌，若此牌是基本牌，此【杀】对你无效。当你使用的【杀】命中一名角色后，你可以重铸其一张手牌。",
	["$DevMeihuoRecast"] = "%from 重铸了 %card",
	["$DevMeihuoRecastOther"] = "%from 重铸了 %to 的 %card",
	["dev_nvshen"] = "女神",
	[":dev_nvshen"] = "当你受到1点伤害后，你可以将一名体力值大于1的角色的1点体力转化为1点护甲；你造成的伤害无视护甲。",
	["@dev_nvshen-invoke"] = "你可以将一名体力值大于1的角色的1点体力转化为1点护甲",
	["#DevNvshenIgnore"] = "%from 的“%arg”触发，无视了 %to 的 %arg2 点护甲",
	
	["dev_fsu"] = "Fsu0413",
	["&dev_fsu"] = "Fs",
	["designer:dev_fsu"] = "Fsu0413",
	["dev_gepi"] = "嗝屁",
	[":dev_gepi"] = "其他角色的准备阶段开始时，你可以令其弃置你的一张牌，若如此做，你令其一个技能（主公技、觉醒技除外）于此回合内无效。"..
				"若以此法无效的技能的描述中不含有“出牌阶段”或不能无效技能，其摸三张牌。",
	
	["dev_hmqgg"] = "hmqgg",  --技能来源于萌战MOD
	["&dev_hmqgg"] = "老A",
	["designer:dev_hmqgg"] = "hmqgg",
	["dev_chaidao"] = "柴刀",
	[":dev_chaidao"] = "当你使用【杀】对一名角色造成伤害时，你可以弃置你装备区里的武器牌，令此伤害+1。当你受到其他角色造成的伤害时，若你的装备区里有武器牌，你可以防止此伤害，"..
					"然后其视为对你使用一张【借刀杀人】，【杀】的目标为该角色。",
	["dev_chaidao:DamageCaused"] = "你是否发动“柴刀”对 %src 伤害+1？",
	["dev_chaidao:DamageInflicted"] = "你是否发动“柴刀”防止 %src 的伤害？",
	
	["dev_tak"] = "takashiro",  --技能来源于脑洞包
	["&dev_tak"] = "高城君",
	["designer:dev_tak"] = "Notify",
	["dev_saodong"] = "骚动",
	[":dev_saodong"] = "当你受到锦囊牌造成的伤害时，你可交给伤害来源一张方块牌，然后将此伤害转移给其。当一名其他角色受到伤害来源不为你的伤害后，你可以将一张红桃牌当【桃】或【无中生有】使用。",
	["@dev_saodong-diamond"] = "你可以交给 %src 一张方块牌，将【%arg】的伤害转移给其",
	["@dev_saodong"] = "你可以将一张红桃牌当【桃】或【无中生有】使用",
	
	["dev_lzx"] = "lzxqqqq",  --技能来源于亚山之殇MOD
	["&dev_lzx"] = "月兔",
	["designer:dev_lzx"] = "lzxqqqq",
	["dev_zhiyu"] = "治愈",
	[":dev_zhiyu"] = "摸牌阶段，你可以放弃摸牌，改为令一名已受伤的其他角色回复1点体力，然后你摸X张牌（X为其已损失的体力值且至多为2）。 ",
	["@dev_zhiyu-invoke"] = "你可以令一名已受伤的其他角色回复1点体力",
	["dev_pinghe"] = "平和",
	[":dev_pinghe"] = "锁定技，当你使用【杀】对目标角色造成伤害后，若其与你势力不同，你弃置其一张手牌，然后若其手牌数：不小于其体力值，你回复1点体力；小于其体力值，你摸一张牌。",
	
	["dev_cheshen"] = "开不了车",  --技能来源于洛吧杀
	["designer:dev_cheshen"] = "开不了车",
	["dev_zhiyin"] = "指引",
	[":dev_zhiyin"] = "摸牌阶段摸牌时，你可以弃置一名其他角色判定区内的一张牌，然后其摸一张牌且你少摸一张牌。",
	["@dev_zhiyin-invoke"] = "你可以弃置一名其他角色装备区或判定区内的一张牌",
	["dev_jiaodao"] = "教导",
	[":dev_jiaodao"] = "当一名其他角色使用的非延时锦囊牌结算完时，若其存活且此牌的目标角色数大于0，你可以令此牌对相同目标再次执行效果（【借刀杀人】、【铁索连环】、【五谷丰登】除外）。",
	["dev_jiaodao:dev_jiaodao"] = "你是否令 %src 的【%arg】再次执行效果？",
	["#DevJiaodaoEffect"] = "%from 的【%arg】 再次执行效果",
	
	["dev_36li"] = "36李",
	["designer:dev_36li"] = "叫什么啊你妹",
	["dev_meigong"] = "美工",
	[":dev_meigong"] = "出牌阶段限一次，你可以令一名其他角色的限定技的可发动次数+1。所有角色的结束阶段开始时会触发两次。",
	["dev_qiliao"] = "弃疗",
	[":dev_qiliao"] = "结束阶段开始时，若弃牌堆里有点数为X的整数倍的牌（X为你的体力值且至少为1），你可以令一名其他角色摸一张牌，然后你可以令其回复1点体力，若如此做，你下一次的回复效果无效。",
	["@dev_qiliao-invoke"] = "你可以发动“弃疗”",
	["dev_qiliao:recover"] = "你是否令 %src 回复1点体力？",
	["#DevQiliaoEffect"] = "%from 的“%arg”触发，%arg2 点回复效果无效",
	
	["dev_tan"] = "任意角的正切",  --技能来源于脑洞包
	["&dev_tan"] = "任意正切",
	["designer:dev_tan"] = "Notify",
	["dev_xuexi"] = "学习",
	[":dev_xuexi"] = "当你于你的出牌阶段内使用牌时，你可以翻开牌堆顶的一张牌，若你本回合未以此法获得过该类型的牌，你获得之。",
	["dev_yukuai"] = "愉快",
	[":dev_yukuai"] = "锁定技，弃牌阶段结束时，你摸X张牌（X为你手牌中缺少的类型数且至少为1）。",
	
	["dev_zhangzheng"] = "张拯明寰",
	["designer:dev_zhangzheng"] = "叫什么啊你妹",
	["dev_geili"] = "给力",
	[":dev_geili"] = "当你回复体力后，你获得一枚“制图”标记。一名角色的回合开始时，你可以弃置一枚“制图”标记，令其本回合的回复效果改为造成等量伤害。",
	["dev_geili_zhitu"] = "制图",
	["#DevGeiliEffect"] = "%from 的“%arg”触发，回复效果改为造成伤害",
	
	["dev_jiaqi"] = "陈家祺大同中心",
	["&dev_jiaqi"] = "家祺",
	["designer:dev_jiaqi"] = "叫什么啊你妹",
	["dev_jiangyou"] = "酱油",
	[":dev_jiangyou"] = "锁定技，游戏开始时，你休眠。所有角色累计使用X张锦囊牌后，若你：处于休眠状态，将你唤醒（X为5）；不处于休眠状态，你休眠（X为10）。" ..
						"当一名开发组角色死亡时，若存活角色数为2，将你唤醒且你失去此技能。",
	["#DevJiangyouEnterXiumian"] = "%from 的“%arg”触发，进入休眠",
	["#DevJiangyouExitXiumian"] = "%from 的“%arg”触发，脱离休眠被唤醒",
	["dev_jiangyou_enter_xiumian"] = "进入休眠",
	["dev_jiangyou_exit_xiumian"] = "脱离休眠",
	["dev_heti"] = "合体",
	[":dev_heti"] = "锁定技，当你被唤醒后，你的手牌上限+1，然后武将牌为“付尼玛”的其他角色的手牌上限+1。当武将牌为“付尼玛”的其他角色死亡时，若你没有副将或你的副将不是“付尼玛”，" ..
					"你将副将变为“付尼玛”（休眠状态下不触发）。",

	["dev_zy"] = "ZY",  --技能来源于脑洞包
	["designer:dev_zy"] = "Notify",
	["dev_juanlao"] = "奆佬",
	[":dev_juanlao"] = "出牌阶段限一次，你可以视为使用本回合你使用过的上一张非转化的非延时类锦囊牌。",
	["dev_yegeng"] = "夜更",
	[":dev_yegeng"] = "锁定技，回合结束后，若你本回合使用的非延时类锦囊牌数不小于3，你进行一个额外回合。",
	
	["dev_jiaoshen"] = "饺神",  --技能来源于高达杀
	["designer:dev_jiaoshen"] = "饺神",
	["dev_jiaoqi"] = "饺气",
	[":dev_jiaoqi"] = "当你使用【杀】指定目标后，若你的装备区有：武器牌或防具牌，你可弃置其装备区内的一张牌；坐骑牌，你可令其抵消此【杀】的方式改为依次使用两张【闪】。 ",
	["dev_jiaoqi:discard"] = "你是否弃置 %src 装备区内一张牌？",
	["dev_jiaoqi:jink"] = "你是否令 %src 需要两张【闪】才能抵消？",
	["#DevJiaoqiJink"] = "%from 需要两张【闪】才能抵消此【杀】",
	["dev_aojiao"] = "傲饺",
	[":dev_aojiao"] = "觉醒技，准备阶段开始时或当你成为【杀】的目标时，若你已受伤且装备区内有牌，你减1点体力上限，然后获得技能“饺猾”。",
	["dev_jiaohua"] = "饺猾",
	[":dev_jiaohua"] = "当你成为【杀】的目标后，你可以将一张与装备区内的牌花色相同的手牌当【杀】使用，若如此做，移除此【杀】的所有目标。",
	["@dev_jiaohua"] = "你可以将一张与装备区内的牌花色相同的手牌当【杀】使用",

	["dev_ysister"] = "你妹大神",  --技能来源于开发组包
	["&dev_ysister"] = "你妹",
	["#dev_ysister"] = "神渣渣",
	["dev_douzha"] = "渣渣",
	[":dev_douzha"] = "锁定技，每当一名其他角色武将牌翻面时，你摸一张牌并将武将牌翻面。",
	["dev_koujiao"] = "抠脚",
	[":dev_koujiao"] = "其他角色回合开始时，可以令你摸一张牌。",

	["dev_luaxs"] = "lua学生",  --技能来源于lua学生的群友包
	["designer:dev_luaxs"] = "lua学生",
	["dev_tumou"] = "图谋",
	[":dev_tumou"] = "当你需要使用一张非延时锦囊牌时，若你本回合未造成过伤害，你可以将一张基本牌当做此锦囊牌使用之。",
	["dev_jiying"] = "机应",
	[":dev_jiying"] = "当你使用/打出牌响应其他角色的牌或其他角色使用/打出牌响应你的牌时，你可以摸一张牌。",

}
