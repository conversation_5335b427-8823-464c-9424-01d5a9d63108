{
"common":
{
    "cardNormalWidth": 93,
    "cardNormalHeight": 130,
    "cardMainArea": [0, 0, 93, 130],
    "cardFrameArea": [-5, -5, 98, 135],
    "cardSuitArea": [1, 17, 21, 17],
    "cardNumberArea": [-2, -1, 27, 28],
	"cardYingbianArea": [5, 30, 85, 50],
    "cardFootnoteArea": [5, 75, 85, 50],
	"cardSkillFlagArea": [30, -50, 100, 70],
    "cardAvatarArea": [26, 67, 40, 40],
    "cardFootnoteFont": ["simsun", [12, 12, 1], 0, [228, 213, 160, 255], 1, 10, [0, 0], [20, 20, 20, 255]],
    "promptInfoSize": [900, 110],
    "promptInfoFont": ["simhei", [20, 20, 4], 20, [255, 255, 255, 255], 2, 10, [0, 0], [7, 50, 78, 255]],
    "chooseGeneralBoxSwitchIconSizeThreshold": 12,
    "chooseGeneralBoxSwitchIconEachRow": 6,
    "chooseGeneralBoxSwitchIconEachRowForTooManyGenerals": 7,
    "chooseGeneralBoxNoIconThreshold": 21,
    "chooseGeneralBoxSparseIconSize": [171, 240],
    "chooseGeneralBoxDenseIconSize": [130, 130],
    "bubbleChatBoxShowAreaSize": [138, 64],
    "tinyAvatarSize": [40, 40],
    // When hp exceeds 5, we use text (e.g. 5/8) instead of drawing more than 5 magatamas. The color
    // and font are specified below.
    // All the font in skin file takes the following format:
    // simple font: ["fontName", fontSize, fontWeight(bold), foreColor(rgba)]
    // shawdow font: ["fontName", fontSize, fontWeight(bold), foreColor(rgba), shadowRadius,
    //                shadowDecadeFactor, shadowOffset, shadowDeepestColor]
    // the shadow radius is the width of shadow on each end; the shadow decade factor specifies how
    // fast shadow will disappear; specifying a shadow radius of 5 but decade factor of 0 is equivalent
    // to specify a solid border of width 5.
    // @todo: adjust these colors!
    "magatamaFont": [
        ["DroidSansFallback", [15, 15, -2], 80, [233, 0, 0, 255], 2, 10, [0, 0], [20, 20, 20, 155]],
        ["DroidSansFallback", [15, 15, -2], 80, [233, 34, 34, 255], 2, 10, [0, 0], [20, 20, 20, 155]],
        ["DroidSansFallback", [15, 15, -2], 80, [233, 116, 34, 255], 2, 10, [0, 0], [20, 20, 20, 155]],
        ["DroidSansFallback", [15, 15, -2], 80, [195, 195, 34, 255], 2, 10, [0, 0], [20, 20, 20, 155]],
        ["DroidSansFallback", [15, 15, -2], 80, [141, 195, 34, 255], 2, 10, [0, 0], [20, 20, 20, 155]],
        ["DroidSansFallback", [15, 15, -2], 80, [66, 174, 34, 255], 2, 10, [0, 0], [20, 20, 20, 155]]
    ],
    // I realized that some MOD might need a wider background area than the foreground magatamas in order
    // to draw certain effect. The following factor specifies the number of invisible magatama to be appended.
    "hpExtraSpaceHolder": 1
},
"room":
{
    "scenePadding": 0,
    "roleBoxHeight": 60,
    "chatTextBoxHeight": 35,
    "logBoxHeightPercentage": 0.6,
    "chatBoxHeightPercentage": 0.4,
    "infoPlaneWidthPercentage": 0.17,
    "photoRoomPadding": 10,
    "photoDashboardPadding": 40,
    "photoHDistance": 32,
    "photoVDistance": 32,
    "discardPileMinWidth": 465,
    "discardPilePadding": 50,
    "minimumSceneSize": [1130, 650], // if smaller than this size, switch to compact skin
    //make sure that default skin's minimum scene size is smaller than maximum scene size in compact skin to avoid deadloop!!!
    "minimumSceneSize-10player": [1050, 800]
},
"photo":
{
    "normalWidth": 157,
    "normalHeight": 181,
    "mainFrameArea": [0, 0, 157, 181],
    "canvasArea": [0, 0, 157, 181],
    "handCardNumIconArea": [-7, 93, 30, 18],
    "handCardFont": ["DroidSansFallback", [12, 12, -1], 20, [255, 255, 255, 255], 1, 30, [0, 0], [20, 20, 20, 200]],
    "equipAreas": [[1, 120, 150, 21], [1, 134, 150, 21], [1, 148, 75, 21], [70, 148, 75, 21], [1, 162, 150, 21]],
    "equipImageArea": [0, 1, 140, 19],
    "equipSuitArea": [117, 2, 21, 17],
    "equipPointArea": [106, -3, 25, 25],
    "horseImageArea": [0, 1, 70, 19],
    "horseSuitArea": [48, 2, 21, 17],
    "horsePointArea": [37, -4, 25, 25],
    "equipPointFontBlack": ["DroidSansFallback", [14, 14, 3], 20, [0, 0, 0, 255], 1, 0, [0, 0], [240, 240, 240, 200]],
    "equipPointFontRed": ["DroidSansFallback", [14, 14, 3], 20, [164, 0, 0, 255], 1, 0, [0, 0], [240, 240, 240, 200]],   
    "delayedTrickFirstRegion": [143, 20, 28, 28],
    "delayedTrickStep": [0, 26],
    "roleComboBoxPos": [130, 1],
    "changePrimaryHeroSkinBtnPos": [25, 40],
    "changeSecondaryHeroSkinBtnPos": [100, 40],
    "borderWidth": 15,
    "avatarImageType": 1,
    "secondaryAvatarImageType": 4,
    "primaryAvatarImageType": 6,
    "circleImageType": 0,
    "avatarArea": [1, 1, 155, 179],
    "secondaryAvatarArea": [78, 1, 77, 179],
    "circleArea": [1, 1, 155, 179],
    "avatarNameArea": [-1, 22, 40, 60],
    "avatarNameFont": ["@simli", [18, 18, 0], 0, [255, 255, 255, 255], 1, 10, [0, 0], [50, 50, 50, 200]],
    "smallAvatarNameArea": [78, 22, 40, 60],
    "smallAvatarNameFont": ["@simli", [18, 18, 0], 0, [255, 255, 255, 255], 1, 10, [0, 0], [50, 50, 50, 200]],
    "kingdomIconArea": [-4, -2, 28, 28],
    "kingdomMaskArea": [0, 12, 18, 80],
    "screenNameArea": [0, 0, 157, 15],
    "screenNameFont": ["simsun", 12, 0, [255, 255, 255, 255], 0, 10, [0, 0], [20, 20, 20, 155]],
    "magatamasHorizontal": false,
    "magatamaSize": [18, 17],
    "magatamaImageArea": [2, 2, 14, 17],
    "magatamasBgVisible": true,
    "magatamasAnchor": ["bottomRight", [156, 194]],
    "cardMoveArea": [-15, 35, 200, 130],
    "phaseArea": [25, 176, 140, 12],
    "onlineStatusArea": [122, 24, 30, 25],
    "onlineStatusBgColor": [0, 0, 0, 0],
    "onlineStatusFont": ["simsun",12, 0, [255, 255, 255, 255], 3, 10, [0, 0], [20, 20, 20, 155]],
    "skillNameArea": [25, 30, 300, 50],
    "skillNameFont": ["simli", 30, 0, [255, 255, 255, 255], 2, 20, [0, 0], [255, 20, 255, 128]],
    "progressBarArea": ["topLeft", "bottomLeft", [0, 6], [157, 13]],
    "progressBarHorizontal": true,
    "privatePileStartPos": [25, 18],
    "privatePileStep": [0, 16],
    "privatePileButtonSize": [80, 16],
    "actionedIconRegion": [100, 68, 52, 21],
    "saveMeIconRegion": [25, 40, 122, 50],
    "votesIconRegion": [53, 66, 50, 50],
    "chainedIconRegion": [0, 82, 156, 19],
    "readyIconRegion": [97, 125, 44, 53],
    "deathIconRegion": [2, 44, 157, 102],
    "drankMaskColor": [250, 0, 0, 115],
    "duanchangMaskColor": [255, 255, 255, 128],
    "deathEffectColor": [50, 50, 50, 255],
    "extraSkillArea": [32, 96, 56, 20],
    "extraSkillFont": ["simli", [16, 16, 3], 0, [255, 255, 255, 255], 2, 100, [0, 0], [102, 16, 120, 255]],
    "extraSkillTextArea": [32, 93, 56, 20]
},
"dashboard":
{
    "leftWidth": 164,
    "rightWidth": 171,
    "normalHeight": 170,
    "floatingAreaHeight": 50,
    "handCardNumIconArea": [-120, 170, 30, 18],
    "handCardFont": ["DroidSansFallback", [12, 12, -4], 20, [255, 255, 255, 255], 1, 30, [0, 0], [20, 20, 20, 200]],
    "equipAreas": [[7, 42, 149, 25], [7, 67, 149, 25], [7, 92, 149, 25], [7, 117, 149, 25], [7, 142, 149, 25]],
    "equipBorderPos": [-6, -6],
    "equipSelectedOffset": [10, 0],
    "equipImageArea": [0, 0, 149, 25],
    "equipSuitArea": [128, 5, 21, 17],
    "equipPointArea": [117, -4, 30, 30],
    "horseImageArea": [0, 0, 149, 25],
    "horseSuitArea": [128, 5, 21, 17],
    "horsePointArea": [117, -5, 30, 30],
    "equipPointFontBlack": ["DroidSansFallback", [14, 14, 3], 20, [0, 0, 0, 255], 1, 0, [0, 0], [240, 240, 240, 200]],
    "equipPointFontRed": ["DroidSansFallback", [14, 14, 3], 20, [164, 0, 0, 255], 1, 0, [0, 0], [240, 240, 240, 200]],
    "delayedTrickFirstRegion": [3, 3, 28, 28],
    "delayedTrickStep": [28, 0],
    // the width of the region to disperse cards when the cards are to be moved to the special pile.
    "disperseWidth": 250,
    "roleComboBoxPos": [-30, 34],
    "changePrimaryHeroSkinBtnPos": [25, 30],
    "changeSecondaryHeroSkinBtnPos": [110, 30],
    "borderWidth": 6,
    "avatarImageType": 1,
    "secondaryAvatarImageType": 4,
    "primaryAvatarImageType": 6,
    "circleImageType": 0,
    "avatarArea": [3, 3, 165, 191],
    "secondaryAvatarArea": [86, 3, 82, 191],
    "circleArea": [19, 3, 134, 191],
    "avatarNameArea": [-38, -9, 60, 40],
    "focusFrameArea": [3, 3, 165, 191],
    "smallAvatarNameArea": [84, 23, 40, 60],
    "avatarNameFont": ["simli", [18, 18, 0], 0, [255, 255, 255, 255], 1, 10, [0, 0], [50, 50, 50, 200]],
    "smallAvatarNameFont": ["@simli", [18, 18, 0], 0, [255, 255, 255, 255], 4, 10, [0, 0], [50, 50, 50, 200]],
    // must be in one of the following format:
    // [offsetX, offestY, sizeX, sizeY]
    // [childAnchor, parentAnchor, [offsetX, offsetY]]
    // [childAnchor, parentAnchor, [offsetX, offsetY], [sizeX, sizeY]]
    // if childAnchor and/or parentAnchor are not set, then area will be aligned using top left corner of both child and parent
    // otherwise, the corner of child specified by "childAnchor" will first be aligned to that of parent specified by "parentAnchor",
    // and offset if applied after the alignment is done. When the 4th parameter [sizeX, sizeY] is specified, fixed size of the area
    // is used; otherwise, it means area's size is variable with the content of that area, the program will adjust the size automatically
    "markTextArea": ["bottomRight", "bottomRight",  [-60, 15]],
    "phaseArea": ["bottomRight", "bottomRight", [-150, 20], [209, 18]],
    "kingdomIconArea": [-63, 1, 28, 25],
    "kingdomMaskArea": [-61, 5, 87, 21],
    "screenNameArea": [0, 0, 157, 25],
    "screenNameFont": ["simsun", 12, 0, [255, 255, 255, 255], 2, 10, [0, 0], [0, 0, 0, 224]],
    "magatamasHorizontal": false,
    "magatamaSize": [23, 30],
    "magatamaImageArea": [0, 3, 22, 26],
    "magatamasBgVisible": true,
    "magatamasAnchor": ["bottomRight", [168, 225]],
    "cardMoveArea": [-50, 65, 200, 130],
    "skillNameArea": [25, 30, 300, 50],
    "skillNameFont": ["simli", 30, 0, [255, 255, 255, 255], 2, 20, [0, 0], [255, 0, 0, 128]],
    "progressBarArea": ["bottomCenter", "bottomCenter", [-58, 0], [262, 15]],
    "progressBarHorizontal": true,
    "privatePileStartPos": [40, 20],
    "privatePileStep": [0, 16],
    "privatePileButtonSize": [80, 16],
    "actionedIconRegion": [84, 30, 52, 21],
    "saveMeIconRegion": [20, 15, 122, 50],
    "votesIconRegion": [50, 52, 50, 50],
    "chainedIconRegion": [3, 90, 141, 19],
    "readyIconRegion": [86, 142, 44, 53],
    "deathIconRegion": ["center", "center", [0, 12], [172, 112]],
    "drankMaskColor": [250, 0, 0, 115],
    "duanchangMaskColor": [255, 255, 255, 128],
    "buttonSetSize": [106, 168],
    "confirmButtonArea": [5, 3, 61, 75],
    "cancelButtonArea": [4, 92, 61, 75],
    "discardButtonArea": [70, 45, 33, 81],
    "trustButtonArea": [68, 132, 36, 35],
    "trustEffectColor": [38, 26, 66],
    "skillDockLeftMargin": 6,
    "skillDockRightMargin": 35,
    "skillDockBottomMargin": 5
},
"skillButton":
{
    "height": 26,
    "width": [134, 67, 45],
    "textArea": [[19, 2, 112, 20], [20, 2, 44, 20], [9, 2, 40, 20]],
    "textAreaDown": [[20, 3, 112, 20], [21, 3, 44, 20], [10, 3, 40, 20]],
    "textFont": [["simkai", [13, 13, 2], 50, [0, 0, 0, 255], 1, 1, [0, 0], [255, 255, 190, 128]],  // wide
                 ["simkai", [13, 13, 1], 50, [0, 0, 0, 255], 1, 1, [0, 0], [255, 255, 190, 128]],  // medium
                 ["simkai", [13, 13, 0], 50, [0, 0, 0, 255], 1, 1, [0, 0], [255, 255, 190, 128]]],  // narrow

    // The following settings override the text color defined above
    // xxButtonColor = [normalColor, downColor, disabledColor, hoverColor]
    "awakenFontColor": [[[255, 255, 255, 255], [0, 0, 0, 155]], // normalColor = [penColor, shadowColor]
                        [[255, 255, 255, 255], [0, 0, 0, 155]], // downColor = [penColor, shadowColor]
                        [[255, 255, 255, 255], [0, 0, 0, 155]], // disableColor = [penColor, shadowColor]
                        [[255, 255, 255, 255], [0, 0, 0, 155]]], // hoverColor = [penColor, shadowColor]
    "compulsoryFontColor": [[[255, 255, 255, 255], [0, 0, 0, 155]],
                            [[255, 255, 255, 255], [0, 0, 0, 155]],
                            [[255, 255, 255, 255], [0, 0, 0, 155]],
                            [[255, 255, 255, 255], [0, 0, 0, 155]]],
    "frequentFontColor": [[[255, 255, 255, 255], [0, 0, 0, 155]],
                          [[255, 255, 255, 255], [0, 0, 0, 155]],
                          [[255, 255, 255, 255], [0, 0, 0, 155]],
                          [[255, 255, 255, 255], [0, 0, 0, 155]]],
    "oneoffFontColor": [[[255, 255, 255, 255], [0, 0, 0, 155]],
                        [[255, 255, 255, 255], [0, 0, 0, 155]],
                        [[255, 255, 255, 255], [0, 0, 0, 155]],
                        [[255, 255, 255, 255], [0, 0, 0, 155]]],
    "proactiveFontColor": [[[255, 255, 255, 255], [0, 0, 0, 155]],
                           [[255, 255, 255, 255], [0, 0, 0, 155]],
                           [[255, 255, 255, 255], [0, 0, 0, 155]],
                           [[255, 255, 255, 255], [0, 0, 0, 155]]],
    "attachedlordFontColor": [[[255, 255, 255, 255], [0, 0, 0, 155]],
                              [[255, 255, 255, 255], [0, 0, 0, 155]],
                              [[255, 255, 255, 255], [0, 0, 0, 155]],
                              [[255, 255, 255, 255], [0, 0, 0, 155]]],
    "changeFontColor": [[[255, 255, 255, 255], [0, 0, 0, 120]],
                        [[255, 255, 255, 255], [0, 0, 0, 120]],
                        [[255, 255, 255, 255], [0, 0, 0, 120]],
                        [[255, 255, 255, 255], [0, 0, 0, 120]]]
}
}
