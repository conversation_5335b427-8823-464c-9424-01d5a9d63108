sgs.ai_skill_discard.kelqchaojue = function(self)
	local cards = self.player:getCards("h")
	cards = sgs.QList2Table(cards)
	if #cards<3 then return {} end
	self:sortByKeepValue(cards)
	local to_cards = {}
   	for i,c in sgs.list(cards)do
		if i>=#to_cards/2 then
			if self:aiUseCard(c).card then continue end
			table.insert(to_cards,c:getEffectiveId())
			break
		end
	end
	return to_cards
end

sgs.ai_skill_discard.kelqchaojue_show = function(self,x,n,o,e,p)
	local cards = self.player:getCards("h")
	cards = sgs.QList2Table(cards)
	self:sortByKeepValue(cards)
	local to_cards = {}
	local cp = self.room:getCurrent()
   	for i,c in sgs.list(cards)do
		if p:contains(c:getSuitString()) and not self:isEnemy(cp) then
			table.insert(to_cards,c:getEffectiveId())
			break
		end
	end
	return to_cards
end

local kelqjunshen={}
kelqjunshen.name="kelqjunshen"
table.insert(sgs.ai_skills,kelqjunshen)
kelqjunshen.getTurnUseCard = function(self)
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)
	for _,h in sgs.list(cards)do
		if h:isRed() then
			local slash = dummyCard()
			slash:setSkillName("kelqjunshen")
			slash:addSubcard(h)
			if slash:isAvailable(self.player) then
				return slash
			end
		end
	end
end

sgs.ai_view_as.kelqjunshen = function(card,player,card_place,class_name)
	if card_place==sgs.Player_PlaceSpecial then return end
	if card:isRed() then
    	return "slash:kelqjunshen[no_suit:0]="..card:getEffectiveId()
	end
end

sgs.ai_skill_choice.kelqjunshen = function(self,choices)
	local items = choices:split("+")
	if table.contains(items,"qizhi") then
		if self.player:getEquips():length()<3 or self:isWeak() or self.player:getEquips():length()/2<=#self:poisonCards("e") then
			return "qizhi"
		end
	end
	return items[2]
end

sgs.ai_skill_use["@@kelqlizhong"] = function(self,prompt)
	local valid,tos = {},{}
	local players = self.player:getAliveSiblings()
    players = self:sort(players)
	if self.player:getMark("kelqlizhong1")~=1 then
		local cards = self.player:getCards("he")
		cards = self:sortByKeepValue(cards) -- 按保留值排序
		for _,h in sgs.list(cards)do
			if h:getTypeId()==3 then
				local n = h:getRealCard():toEquipCard():location()
				for _,p in sgs.list(players)do
					if table.contains(tos,p:objectName()) then continue end
					if p:hasEquipArea(n) and p:getEquip(n)==nil then
						if #self:poisonCards({h})>0 then
							if self:isEnemy(p) or not self:isFriend(p) and #self.enemies<1 then
								table.insert(valid,h:getEffectiveId())
								table.insert(tos,p:objectName())
								break
							end
						elseif self:isFriend(p) then
							table.insert(valid,h:getEffectiveId())
							table.insert(tos,p:objectName())
							break
						end
					end
				end
			end
		end
		if #tos<1 then
			if self.player:getMark("kelqlizhong1")==2 then return end
			for _,p in sgs.list(players)do
				if self:isFriend(p) and p:hasEquip()
				then table.insert(tos,p:objectName()) end
			end
		end
		if #tos<1 then table.insert(tos,self.player:objectName()) end
	else
		for _,p in sgs.list(players)do
			if self:isFriend(p) and p:hasEquip()
			then table.insert(tos,p:objectName()) end
		end
		if #tos<1 then table.insert(tos,self.player:objectName()) end
	end
	if #tos<1 then return end
	return string.format("#kelqlizhongcard:%s:->%s",table.concat(valid,"+"),table.concat(tos,"+"))
end

sgs.ai_view_as.kelqlizhongUse = function(card,player,card_place,class_name)
	if card_place~=sgs.Player_PlaceEquip then return end
   	return "nullification:_kelqlizhong[no_suit:0]="..card:getEffectiveId()
end

sgs.ai_skill_invoke.kelqjuesui = function(self,data)
	local target = data:toPlayer()
	if target then
		return self:isFriend(target)
		or not self:isEnemy(target) and #self.enemies>=#self.friends
	else
		return true
	end
end


local kelqjuesuiUse={}
kelqjuesuiUse.name="kelqjuesuiUse"
table.insert(sgs.ai_skills,kelqjuesuiUse)
kelqjuesuiUse.getTurnUseCard = function(self)
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)
	for _,h in sgs.list(cards)do
		if h:isBlack() and h:getTypeId()>1 then
			local slash = dummyCard()
			slash:setSkillName("_kelqjuesui")
			slash:addSubcard(h)
			if slash:isAvailable(self.player) then
				return slash
			end
		end
	end
end

sgs.ai_view_as.kelqjuesuiUse = function(card,player,card_place,class_name)
	if card_place==sgs.Player_PlaceSpecial then return end
	if card:isRed() and card:getTypeId()>1 then
    	return "slash:_kelqjuesui[no_suit:0]="..card:getEffectiveId()
	end
end

sgs.ai_target_revises.kelqjuwu = function(to,card,self)
    if card:objectName()=="slash" then
		local num = 0
		for _, p in sgs.qlist(self.room:getAllPlayers()) do
			if self.player:inMyAttackRange(p) then
				num = num + 1
			end
		end
		if num>=3
		then return true end
	end
end

sgs.ai_skill_invoke.kelqshouxiang = function(self,data)
	local numt = 0
	for _, p in sgs.qlist(self.room:getAllPlayers()) do
		if p:inMyAttackRange(self.player) then
			numt = numt + 1
		end
	end
	if numt>1 then
		return #self:getTurnUse()<2
	end
end

sgs.ai_skill_use["@@kelqshouxiang"] = function(self,prompt)
	local valid,tos = {},{}
    self:sort(self.friends_noself,nil,true)
	local n = self.player:getMark("kelqshouxiangNum")
	local cards = self.player:getCards("h")
	cards = self:sortByKeepValue(cards) -- 按保留值排序
	if #self.friends_noself<2 then
		local players = self.player:getAliveSiblings()
		players = self:sort(players,nil,true)
		for _,h in sgs.list(cards)do
			for _,p in sgs.list(players)do
				if table.contains(tos,p:objectName()) or #tos>=n then continue end
				if self:isFriend(p) or not self:isEnemy(p) and #self.enemies>0 then
					table.insert(valid,h:getEffectiveId())
					table.insert(tos,p:objectName())
					break
				end
			end
		end
	else
		for _,h in sgs.list(cards)do
			for _,p in sgs.list(self.friends_noself)do
				if table.contains(tos,p:objectName()) or #tos>=n then continue end
				table.insert(valid,h:getEffectiveId())
				table.insert(tos,p:objectName())
				break
			end
		end
	end
	if #tos<1 then return end
	return string.format("#kelqshouxiangcard:%s:->%s",table.concat(valid,"+"),table.concat(tos,"+"))
end



