--花木兰

local kenewshixie_skill = {}
kenewshixie_skill.name = "kenewshixie"
table.insert(sgs.ai_skills, kenewshixie_skill)
kenewshixie_skill.getTurnUseCard = function(self)
	local alls = self.room:getAllPlayers()
	local needrec = 0
	local needhelp = 0
	local needda = 0
	local needchai = 0
	for _,p in sgs.qlist(alls) do
		if self:isFriend(p) and (p:getMark("banshixie-Clear") == 0) then
			needhelp = 1
			if p:isWounded() then
			    needrec = 1
			end
		end
		if self:isEnemy(p) and (p:getMark("banshixie-Clear") == 0) then
			needda = 1
			needchai = 1
		end
	end
	--打伤害
	if (needda == 1) and (self.player:getMark("useshixiespade-Clear") == 0) then
		local card_id
		local cards = self.player:getHandcards()
		cards = sgs.QList2Table(cards)
		self:sortByKeepValue(cards)
		local to_throw = sgs.IntList()
		for _, acard in ipairs(cards) do
			if (acard:getSuit() == sgs.Card_Spade) then
				to_throw:append(acard:getEffectiveId())
			end
		end
		card_id = to_throw:at(0)
		if (card_id > 0) then
			return sgs.Card_Parse("#kenewshixieCard:"..card_id..":")
		end
	end
	--加血
	if (needrec == 1) and (self.player:getMark("useshixieheart-Clear") == 0) then
		local card_id
		local cards = self.player:getHandcards()
		cards = sgs.QList2Table(cards)
		self:sortByKeepValue(cards)
		local to_throw = sgs.IntList()
		for _, acard in ipairs(cards) do
			if (acard:getSuit() == sgs.Card_Heart) then
				to_throw:append(acard:getEffectiveId())
			end
		end
		card_id = to_throw:at(0)
		if (card_id > 0) then
			return sgs.Card_Parse("#kenewshixieCard:"..card_id..":")
		end
	end
	--摸牌
	if (needhelp == 1) and (self.player:getMark("useshixiediamond-Clear") == 0) then
		local card_id
		local cards = self.player:getHandcards()
		cards = sgs.QList2Table(cards)
		self:sortByKeepValue(cards)
		local to_throw = sgs.IntList()
		for _, acard in ipairs(cards) do
			if (acard:getSuit() == sgs.Card_Diamond) then
				to_throw:append(acard:getEffectiveId())
			end
		end
		card_id = to_throw:at(0)
		if (card_id > 0) then
			return sgs.Card_Parse("#kenewshixieCard:"..card_id..":")
		end
	end
	--拆牌
	if (needchai == 1) and (self.player:getMark("useshixieclub-Clear") == 0) then
		local card_id
		local cards = self.player:getHandcards()
		cards = sgs.QList2Table(cards)
		self:sortByKeepValue(cards)
		local to_throw = sgs.IntList()
		for _, acard in ipairs(cards) do
			if (acard:getSuit() == sgs.Card_Club) then
				to_throw:append(acard:getEffectiveId())
			end
		end
		card_id = to_throw:at(0)
		if (card_id > 0) then
			return sgs.Card_Parse("#kenewshixieCard:"..card_id..":")
		end
	end
	return nil
end


sgs.ai_skill_use_func["#kenewshixieCard"] = function(card, use, self)
	local idd =  card:getEffectiveId()
	if sgs.Sanguosha:getCard(idd):isRed() then
		self:sort(self.friends,"hp")
		for _,ep in sgs.list(self.friends)do
			if (ep:getMark("banshixie-Clear") == 0) then
				use.card = card
				use.to:append(ep)
				return
			end
		end
	else
		self:sort(self.enemies,"hp")
		for _,ep in sgs.list(self.enemies)do
			if (ep:getMark("banshixie-Clear") == 0) then
				use.card = card
				use.to:append(ep)
				return
			end
		end
	end
end

sgs.ai_use_value.kenewshixieCard = 8.5
sgs.ai_use_priority.kenewshixieCard = 9.5
sgs.ai_card_intention.kenewshixieCard = 80

--[[function sgs.ai_cardneed.kenewshixieCard(to, card)
	return card:isRed()
end]]

sgs.ai_skill_invoke.kenewcongrong = function(self, data)
	local room = self.player:getRoom()
	for _,p in sgs.qlist(room:getAllPlayers()) do
		if p:hasFlag("ifwantkenewcongrong") and self:isFriend(p) then
	        return true
		end
	end
end

--娘张飞
sgs.ai_skill_invoke.kenewfuyi = function(self, data)
	local room = self.player:getRoom()
	if self.player:hasFlag("fuyifrom") 
	or self.player:hasFlag("fuyieff") 
	or self.player:hasFlag("fuyiadd")then
	    return true
	else
		--背水情况
		for _,p in sgs.qlist(room:getOtherPlayers(self.player)) do
			if (self:isWeak(p) or self:isWeak()) and self:isFriend(p) then
				return true
			end
		end
		--添加敌人目标
		for _,p in sgs.qlist(room:getOtherPlayers(self.player)) do
			if self:isEnemy(p) then
				return true
			end
		end
	end
end

sgs.ai_skill_choice.kenewfuyi = function(self,choices)
	local items = choices:split("+")
	local room = self.player:getRoom()
	if self.player:hasFlag("fuyifrom") then
		if self.player:hasFlag("fuyifromda") then
			local num = math.random(0,1)
			if num == 0 then
			    return "add"
			else
				return "eff"
			end
		else
			return "add"
		end
	else
		--[[for _,p in sgs.qlist(room:getOtherPlayers(self.player)) do
			if (self:isWeak(p) or self:isWeak()) and self:isFriend(p) then
				return "beishui"
			end
		end]]
		if self.player:hasFlag("fuyieff") then
			return "eff"
		elseif self.player:hasFlag("fuyiadd") then
			return "add"
		end
		return "add"
	end
end

sgs.ai_skill_playerschosen.kenewfuyi = function(self, targets, max, min)
    local selected = sgs.SPlayerList()
    local n = max
    local can_choose = sgs.QList2Table(targets)
    self:sort(can_choose, "defense")
	if self.player:hasFlag("beishuifuyi") then
		for _,target in ipairs(can_choose) do
			if self:isFriend(target) then
				selected:append(target)
				n = n - 1
			end
			if n <= 0 then break end
		end
	else
		for _,target in ipairs(can_choose) do
			if self:isEnemy(target) then
				selected:append(target)
				n = n - 1
			end
			if n <= 0 then break end
		end
	end
    return selected
end

sgs.ai_skill_invoke.kenewzhenyi = function(self, data)
	local room = self.player:getRoom()
	for _,p in sgs.qlist(room:getAllPlayers()) do
		if p:hasFlag("ifwantkenewzhenyi") and self:isFriend(p) then
			return true
		end
	end
end

--娘姜维

sgs.ai_skill_playerschosen.kenewjizhi = function(self, targets, max, min)
    local selected = sgs.SPlayerList()
    local n = max
    local can_choose = sgs.QList2Table(targets)
    self:sort(can_choose, "defense")
	if (n > 0) then
		for _,target in ipairs(can_choose) do
			if self:isFriend(target) then
				selected:append(target)
				n = n - 1
			end
			if n <= 0 then break end
		end
	end
    return selected
end

local kenewshixie_skill = {}
kenewshixie_skill.name = "kenewshixie"
table.insert(sgs.ai_skills, kenewshixie_skill)
kenewshixie_skill.getTurnUseCard = function(self)
	if (needda == 1) and (self.player:getMark("useshixiespade-Clear") == 0) then
		local card_id
		local cards = self.player:getHandcards()
		cards = sgs.QList2Table(cards)
		self:sortByKeepValue(cards)
		local to_throw = sgs.IntList()
		for _, acard in ipairs(cards) do
			if (acard:getSuit() == sgs.Card_Spade) then
				to_throw:append(acard:getEffectiveId())
			end
		end
		card_id = to_throw:at(0)
		if (card_id > 0) then
			return sgs.Card_Parse("#kenewshixieCard:"..card_id..":")
		end
	end
	return nil
end


sgs.ai_skill_use_func["#kenewshixieCard"] = function(card, use, self)
	local idd =  card:getEffectiveId()
	self:sort(self.enemies,"hp")
	for _,ep in sgs.list(self.enemies)do
		if (ep:getMark("banshixie-Clear") == 0) then
			use.card = card
			use.to:append(ep)
			return
		end
	end
end

sgs.ai_use_value.kenewshixieCard = 8.5
sgs.ai_use_priority.kenewshixieCard = 9.5
sgs.ai_card_intention.kenewshixieCard = 80



