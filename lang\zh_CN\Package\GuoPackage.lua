-- translation for Guo Package

return {
	["guo"] = "文德武备·果",
	
	["jin_simaz<PERSON>"] = "司马昭",
	["#jin_simazhao"] = "晋文帝",
	["illustrator:jin_simaz<PERSON>"] = "",
	["jintuishi"] = "推弑",
	[":jintuishi"] = "隐匿技，当你于其他角色的回合登场后，此回合结束时，你可选择其攻击范围内的一名角色，令其选择一项：对该角色使用一张【杀】；或你对其造成1点伤害。",
	["@jintuishi-invoke"] = "请选择 %src 使用【杀】的目标",
	["#JinTuishiSlash"] = "%from 发动“%arg”，选择了 %to 成为【%arg2】的目标",
	["@jintuishi_slash"] = "请对 %src 使用一张【杀】",
	["jinchoufa"] = "筹伐",
	[":jinchoufa"] = "出牌阶段限一次，你可以展示一名其他角色的一张手牌，其手牌中与此牌不同类型的牌均视为【杀】直到其回合结束。",
	["jinzhaoran"] = "昭然",
	[":jinzhaoran"] = "出牌阶段开始时，你可令你的手牌于此阶段内对其他角色可见，然后你于此阶段失去任意花色的最后一张手牌后，你摸一张牌或弃置一名其他角色的一张牌（本阶段每种花色限一次）。"..
						"\n<font color=\"red\"><b>◆查看手牌方法：鼠标右键→查看已知牌</b></font>",
	["@jinzhaoran-discard"] = "请弃置一名其他角色的一张牌，否则你摸一张牌",
	["jinchengwu"] = "成务",
	[":jinchengwu"] = "主公技，锁定技，其他晋势力角色攻击范围内的角色视为在你的攻击范围内。",
	
	["jin_wangyuanji"] = "王元姬",
	["#jin_wangyuanji"] = "文明皇后",
	["illustrator:jin_wangyuanji"] = "",
	["jinshiren"] = "识人",
	[":jinshiren"] = "隐匿技，当你于其他角色的回合登场后，若当前回合角色有手牌，你可以对其发动“宴戏”。",
	["jinyanxi"] = "宴戏",
	[":jinyanxi"] = "出牌阶段限一次，你可以令一名其他角色的一张随机手牌与牌堆顶的两张牌混合，你猜测哪张牌来自其手牌。若你：猜对，你获得这三张牌；猜错，你获得你猜测的牌。你以此法获得的牌本回合不计入手牌上限。",
	
	["jin_duyu"] = "杜预",
	["#jin_duyu"] = "文成武德",
	["illustrator:jin_duyu"] = "",
	["jinsanchen"] = "三陈",
	[":jinsanchen"] = "出牌阶段限一次，你可以令一名角色摸三张牌，然后其弃置三张牌。若其以此法弃置的牌种类均不同，其摸一张牌，且你本阶段可以额外发动一次“三陈”（本回合每名角色限一次）。",
	["jinsanchen-discard"] = "请弃置三张牌",
	["jinzhaotao"] = "昭讨",
	[":jinzhaotao"] = "觉醒技，回合开始时，若你发动过至少三次“三陈”，你减1点体力上限，获得“破竹”。",
	["#JinZhaotaoWake"] = "%from 已发动过 %arg 次“<font color=\"yellow\"><b>三陈</b></font>”，触发“%arg2”觉醒",
	["jinpozhu"] = "破竹",
	[":jinpozhu"] = "出牌阶段，你可将一张手牌当【出其不意】使用，若此【出其不意】未造成伤害，本回合此技能无效。",
	
	["jin_weiguan"] = "卫瓘",
	["#jin_weiguan"] = "兰陵郡公",
	["illustrator:jin_weiguan"] = " ",
	["jinzhongyun"] = "忠允",
	[":jinzhongyun"] = "锁定技，每个回合限一次，当你受到伤害或回复体力后，若你的体力值与你的手牌数相等，你回复1点体力或对攻击范围内的一名角色造成1点伤害；"..
						"每个回合限一次，当你获得或失去手牌后，若你的体力值与你的手牌数相等，你摸一张牌或弃置一名其他角色的一张牌。",
	["jinzhongyun:recover"] = "回复1点体力",
	["jinzhongyun:damage"] = "对攻击范围内的一名角色造成1点伤害",
	["jinzhongyun:draw"] = "摸一张牌",
	["jinzhongyun:discard"] = "弃置一名其他角色的一张牌",
	["@jinzhongyun-damage"] = "请对攻击范围内的一名角色造成1点伤害",
	["@jinzhongyun-discard"] = "请弃置一名其他角色的一张牌",
	["jinzhongyun_discard"] = "忠允",
	["jinshenpin"] = "神品",
	[":jinshenpin"] = "当一名角色的判定牌生效前，你可以打出一张与判定牌颜色不同的牌代替之。",
	["@jinshenpin-card"] = CommonTranslationTable["@askforretrial"],
	
	["jin_xuangongzhu"] = "宣公主",
	["#jin_xuangongzhu"] = "高陵公主",
	["illustrator:jin_xuangongzhu"] = "",
	["jingaoling"] = "高陵",
	[":jingaoling"] = "隐匿技，当你于其他角色的回合内登场后，你可令一名角色回复1点体力。",
	["@jingaoling-invoke"] = "你可令一名角色回复1点体力",
	["jinqimei"] = "齐眉",
	[":jinqimei"] = "准备阶段，你可选择一名其他角色，你获得以下效果直到你下回合开始：1.你或该角色手牌数变化后，若与对方手牌数相等，对方摸1张牌；2.你或该角色体力值变化后，若与对方体力值相等，对方摸1张牌。每回合每项限一次。",
	["@jinqimei-invoke"] = "你可以发动“齐眉”",
	["jinqimei_self"] = "齐眉",
	[":&jinqimei_self"] = "标记了%dest",
	["jinzhuiji"] = "追姬",
	[":jinzhuiji"] = "出牌阶段开始时，你可以选择一项：1.回复1点体力值，然后此阶段结束时弃置两张牌；2.摸两张牌，然后此阶段结束时失去1点体力。",
	["jinzhuiji:recover"] = "回复1点体力值，然后此阶段结束时弃置两张牌",
	["jinzhuiji:draw"] = "摸两张牌，然后此阶段结束时失去1点体力",
}
