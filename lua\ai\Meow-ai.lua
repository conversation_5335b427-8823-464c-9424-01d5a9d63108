--逗喵
sgs.ai_skill_invoke.Meowdoumiao = function(self, data)
	return true
end
sgs.ai_skill_playerchosen.Meowdoumiao = function(self, targets)
	self:updatePlayers()
	self:sort(self.enemies, "value")
	for _, target in sgs.qlist(targets) do
		if not self:isFriend(target) then
			return target
		end
	end
end
sgs.ai_playerchosen_intention.Meowdoumiao = function(self, from, to)
	sgs.updateIntention(from, to, 20)
end
sgs.ai_skill_invoke.MeowBeige = function(self, data)
	self:updatePlayers()
	local damage = data:toDamage()
	if self:isFriend(damage.to) or not self:isFriend(damage.from)then
		return true
	else
		return false
	end  
end
sgs.ai_skill_choice.MeowBeige=function(self, choices, data)
	self:updatePlayers()
	local damage = data:toDamage()
	local top = damage.to
	local fromp = damage.from
	if fromp:faceUp() and top:getHp()>=2 and damage and math.random() > 0.2 then
		return "Spade"
	end
	if top:isWounded() and math.random() > 0.2 then
		return "Heart"
	end
	local tp = top:getHandcardNum()
	local fp = fromp:getHandcardNum()
	local n = (math.exp((2*fp-tp)/(fp+tp+1)+1/3)-1)/2-1
	if not from:isNude() and math.random() > n then
		return "Diamond"
	else
		return "Club"
	end
end
sgs.ai_playerchosen_intention.MeowBeige = function(self, from, to)
	sgs.updateIntention(from, to, -60)
end


local Meowlijian_skill = {}
Meowlijian_skill.name = "Meowlijian"
table.insert(sgs.ai_skills, Meowlijian_skill)
Meowlijian_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("MeowlijianCard") 
	or self.player:isNude() 
	or self.room:getAlivePlayers():length() < 3 then
		return
	end
	return sgs.Card_Parse("#MeowlijianCard:.:")
end

sgs.ai_skill_use_func["#MeowlijianCard"] = function(card, use, self)
	if self.player:hasUsed("#MeowlijianCard") then return end
	self:updatePlayers()
	local room = self.room
	local Other = room:getOtherPlayers(self.player)
	local Enemy = sgs.SPlayerList()
	local player = self.player
	local n =player:getHandcardNum()+player:getEquips():length()
	for _, p in sgs.qlist(Other) do
		if not self:isFriend(p) then
			Enemy:append(p)
		end
	end
	if (Enemy:length() > 0) then
		for _, p in sgs.qlist(Enemy) do
			use.card = card
			if use.to then
				use.to:append(p)
			end
		end
	end	
	return
end

sgs.ai_use_value.MeowlijianCard = 17
sgs.ai_use_priority.MeowlijianCard = 5.1
sgs.ai_card_intention.MeowlijianCard = 60

sgs.ai_skill_invoke.MeowQieting = function(self, data)
	return true
end

sgs.ai_skill_choice.MeowQieting=function(self, choices, data)
	local target = self.room:getCurrent()
	local player = self.player
	if self:isFriend(target) then
		return "draw"
	end
	local id = self:askForCardChosen(target, "e", "get")
	if id then
		for i = 0, 4,1 do
			if target:getEquip(i) 
			and target:getEquip(i):getEffectiveId() == id 
			and string.find(choices, i) then
				return i
			end
		end
	end
end


local MeowGuose_skill = {}
MeowGuose_skill.name = "MeowGuose"
table.insert(sgs.ai_skills, MeowGuose_skill)
MeowGuose_skill.getTurnUseCard = function(self)
	if self.player:getMark("MeowGuoseUsed") >= 4 then return end
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	self:sortByUseValue(cards, true)
	local card = nil
	for _,c in ipairs(cards) do
		if c:getSuit() == sgs.Card_Diamond then
			card = c
			break
		end
	end
	if not card then return nil end
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	local card_str = ("Indulgence:MeowGuose[%s:%s]=%d"):format(suit, number, card_id)
	local Indulgence = sgs.Card_Parse(card_str)
	assert(Indulgence)
	return Indulgence
end

sgs.ai_skill_use_func.MeowGuoseCard = function(card, use, self)
	self:sort(self.friends)
	local id = card:getEffectiveId()
	local indul_only = self.player:handCards():contains(id)
	local rcard = sgs.Sanguosha:getCard(id)
	if not indul_only and not self.player:isJilei(rcard) then
		sgs.ai_use_priority.MeowGuoseCard = 5.5
		for _, friend in ipairs(self.friends) do
			if friend:containsTrick("Indulgence") and self:willSkipPlayPhase(friend)
				and not friend:hasSkills("shensu|qingyi|qiaobian") and (self:isWeak(friend) or self:getOverflow(friend) > 1) then
				for _, c in sgs.qlist(friend:getJudgingArea()) do
					if c:isKindOf("Indulgence") and self.player:canDiscard(friend, card:getEffectiveId()) then
						use.card = card
						if use.to then use.to:append(friend) end
						return
					end
				end
			end
		end
	end

	local indulgence = sgs.Sanguosha:cloneCard("Indulgence")
	indulgence:addSubcard(id)
	indulgence:deleteLater()
	if not self.player:isLocked(indulgence) then
		sgs.ai_use_priority.MeowGuoseCard = sgs.ai_use_priority.Indulgence
		local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
		self:useCardIndulgence(indulgence, dummy_use)
		if dummy_use.card and dummy_use.to:length() > 0 then
			use.card = card
			if use.to then use.to:append(dummy_use.to:first()) end
			return
		end
	end

	sgs.ai_use_priority.MeowGuoseCard = 5.5
	if not indul_only and not self.player:isJilei(rcard) then
		for _, friend in ipairs(self.friends) do
			if friend:containsTrick("Indulgence") and self:willSkipPlayPhase(friend) then
				for _, c in sgs.qlist(friend:getJudgingArea()) do
					if c:isKindOf("Indulgence") and self.player:canDiscard(friend, card:getEffectiveId()) then
						use.card = card
						if use.to then use.to:append(friend) end
						return
					end
				end
			end
		end
	end

	if not indul_only and not self.player:isJilei(rcard) then
		for _, friend in ipairs(self.friends) do
			if friend:containsTrick("Indulgence") then
				for _, c in sgs.qlist(friend:getJudgingArea()) do
					if c:isKindOf("Indulgence") and self.player:canDiscard(friend, card:getEffectiveId()) then
						use.card = card
						if use.to then use.to:append(friend) end
						return
					end
				end
			end
		end
	end
end

sgs.ai_use_priority.MeowGuoseCard = 6.5
sgs.ai_use_value.MeowGuoseCard = 6
sgs.ai_card_intention.MeowGuoseCard = -60

function sgs.ai_cardneed.MeowGuose(to, card)
	return card:getSuit() == sgs.Card_Diamond
end

sgs.MeowGuoseCard_suit_value = {
	diamond = 3.9
}
--[[
local MeowJieyi_skill={}
MeowJieyi_skill.name = "MeowJieyi"
table.insert(sgs.ai_skills, MeowJieyi_skill)
MeowJieyi_skill.getTurnUseCard=function(self)
	if self.player:hasUsed("#MeowJieyiCard") then return end
	if self.player:isNude() then return end
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	self:sortByUseValue(cards, true)
	local card = nil
	for _,c in ipairs(cards) do
		card = c
		break
	end
	if not card then return nil end
	local card_id = card:getEffectiveId()
	return sgs.Card_Parse("#MeowJieyiCard:"..card_id..":")
end
sgs.ai_skill_use_func.MeowjieyiCard = function(card, use, self)
	self:updatePlayers()
	self:sort(self.friends_noself, "hp")
	for _, friend in ipairs(self.friends_noself) do
		if use.to then use.to:append(friend) end
	end
	return
end

sgs.ai_use_value.MeowjieyiCard = 7
sgs.ai_use_priority.MeowjieyiCard = 19.1
sgs.ai_card_intention.MeowjieyiCard = -60
]]
sgs.ai_skill_invoke.MeowXiaoji = function(self, data)
	return true
end
sgs.ai_skill_choice.JieyiCard=function(self, choices, data)
	return "yes"
end
sgs.ai_skill_use["@@MeowLiuli"] = function(self, prompt, method)
	local others = self.room:getOtherPlayers(self.player)
	local slash = self.player:getTag("MeowLiuli-card"):toCard()
	others = sgs.QList2Table(others)
	local source
	for _, player in ipairs(others) do
		if player:hasFlag("MeowLiuliSlashSource") then
			source = player
			break
		end
	end
	self:sort(self.enemies, "defense")

	local doMeowLiuli = function(who)
		if not self:isFriend(who) and who:hasSkills("leiji|nosleiji|olleiji")
			and (self:hasSuit("spade", true, who) or who:getHandcardNum() >= 3)
			and (getKnownCard(who, self.player, "Jink", true) >= 1 or self:hasEightDiagramEffect(who)) then
			return "."
		end

		local cards = self.player:getCards("h")
		cards = sgs.QList2Table(cards)
		self:sortByKeepValue(cards)
		for _, card in ipairs(cards) do
			if not self.player:isCardLimited(card, method) and self.player:canSlash(who) then
				if self:isFriend(who) and not (isCard("Peach", card, self.player) or isCard("Analeptic", card, self.player)) then
					return "@MeowLiuliCard="..card:getEffectiveId().."->"..who:objectName()
				else
					return "@MeowLiuliCard="..card:getEffectiveId().."->"..who:objectName()
				end
			end
		end

		local cards = self.player:getCards("e")
		cards=sgs.QList2Table(cards)
		self:sortByKeepValue(cards)
		for _, card in ipairs(cards) do
			local range_fix = 0
			if card:isKindOf("Weapon") then range_fix = range_fix + sgs.weapon_range[card:getClassName()] - self.player:getAttackRange(false) end
			if card:isKindOf("OffensiveHorse") then range_fix = range_fix + 1 end
			if not self.player:isCardLimited(card, method) and self.player:canSlash(who, nil, true, range_fix) then
				return "@MeowLiuliCard=" .. card:getEffectiveId() .. "->" .. who:objectName()
			end
		end
		return "."
	end

	for _, enemy in ipairs(self.enemies) do
		if not (source and source:objectName() == enemy:objectName()) then
			local ret = doMeowLiuli(enemy)
			if ret ~= "." then return ret end
		end
	end

	for _, player in ipairs(others) do
		if self:objectiveLevel(player) == 0 and not (source and source:objectName() == player:objectName()) then
			local ret = doMeowLiuli(player)
			if ret ~= "." then return ret end
		end
	end


	self:sort(self.friends_noself, "defense")
	self.friends_noself = sgs.reverse(self.friends_noself)


	for _, friend in ipairs(self.friends_noself) do
		if not self:slashIsEffective(slash, friend) or self:findLeijiTarget(friend, 50, source) then
			if not (source and source:objectName() == friend:objectName()) then
				local ret = doMeowLiuli(friend)
				if ret ~= "." then return ret end
			end
		end
	end

	for _, friend in ipairs(self.friends_noself) do
		if self:needToLoseHp(friend, source, true) or self:getDamagedEffects(friend, source, true) then
			if not (source and source:objectName() == friend:objectName()) then
				local ret = doMeowLiuli(friend)
				if ret ~= "." then return ret end
			end
		end
	end

	if (self:isWeak() or self:hasHeavySlashDamage(source, slash)) and source:hasWeapon("axe") and source:getCards("he"):length() > 2
	  and not self:getCardId("Peach") and not self:getCardId("Analeptic") then
		for _, friend in ipairs(self.friends_noself) do
			if not self:isWeak(friend) then
				if not (source and source:objectName() == friend:objectName()) then
					local ret = doMeowLiuli(friend)
					if ret ~= "." then return ret end
				end
			end
		end
	end

	if (self:isWeak() or self:hasHeavySlashDamage(source, slash)) and not self:getCardId("Jink") then
		for _, friend in ipairs(self.friends_noself) do
			if not self:isWeak(friend) or (self:hasEightDiagramEffect(friend) and getCardsNum("Jink", friend) >= 1) then
				if not (source and source:objectName() == friend:objectName()) then
					local ret = doMeowLiuli(friend)
					if ret ~= "." then return ret end
				end
			end
		end
	end
	return "."
end

sgs.ai_card_intention.MeowLiuliCard = function(self, card, from, to)
	sgs.ai_MeowLiuli_effect = true
	if not hasExplicitRebel(self.room) then sgs.ai_MeowLiuli_user = from
	else sgs.ai_MeowLiuli_user = nil end
end

function sgs.ai_slash_prohibit.MeowLiuli(self, from, to, card)
	if self:isFriend(to, from) then return false end
	if from:hasFlag("NosJiefanUsed") then return false end
	if to:isNude() then return false end
	for _, friend in ipairs(self:getFriendsNoself(from)) do
		if to:canSlash(friend, card) and self:slashIsEffective(card, friend, from) then return true end
	end
end

function sgs.ai_cardneed.MeowLiuli(to, card)
	return to:getCards("he"):length() <= 2
end



local MeowQingguo_skill = {}
MeowQingguo_skill.name = "MeowQingguo"
table.insert(sgs.ai_skills, MeowGuose_skill)
MeowQingguo_skill.getTurnUseCard = function(self)
	if self.player:getMark("MeowQingguoUsed") >= 4 then return end
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	self:sortByUseValue(cards, true)
	local card = nil
	for _,c in ipairs(cards) do
		if card:isKindOf("Jink") then
			card = c
			break
		end
	end
	if not card then return nil end
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	local card_str = ("peach:MeowQingguo[%s:%s]=%d"):format(suit, number, card_id)
	local peach = sgs.Card_Parse(card_str)
	assert(peach)
	return peach
end
sgs.ai_view_as.MeowQingguo = function(card, player, card_place)
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	if card:isBlack() then
		return ("jink:MeowQingguo[%s:%s]=%d"):format(suit, number, card_id)
	end
	if card:isKindOf("Jink") and not player:hasSkill("Meowdoumiao") and card:isRed() and player:getPhase()==sgs.Player_NotActive then
		return ("peach:MeowQingguo[%s:%s]=%d"):format(suit, number, card_id)
	end
end

sgs.ai_skill_invoke.MeowJueqing = function(self, data)
	local damage = data:toDamage()
	if self:isFriend(damage.to) then return false end
	if self.player:getHp()>=2 then
		return true
	end
	return false
end
sgs.ai_playerchosen_intention.MeowJueqing = function(self, from, to)
	sgs.updateIntention(from, to, 60)
end
sgs.ai_skill_invoke.MeowZhenlie = function(self, data)
	local use = data:toCardUse()
	if self:isFriend(use.from) and self.player:getHp()>=2 then
		return false
	end
	return true
end
sgs.ai_playerchosen_intention.MeowZhenlie = function(self, from, to)
	sgs.updateIntention(from, to, 40)
end
sgs.ai_skill_playerchosen.MeowMiji = function(self, targets)
	local n = 0
	local t
	for _, p in sgs.qlist(targets) do
		if p:getLostHp()>=n then
			n=p:getLostHp()
			t=p
		end
	end
	return(p)
end
--[[烈刃（未完成）
sgs.ai_cardneed.MeowLieren = function(to, card, self)
	return isCard("Slash", card, to) and getKnownCard(to, self.player, "Slash", true) == 0
end
sgs.ai_skill_invoke.MeowLieren = function(self, data)
	self:updatePlayers()
	local use = data:toCardUse()
	if self.player:isKongcheng() then return false end
	if use and (not self:isFriend(use.to) or self:isEnemy(use.to)) then
		return true
	else
		return false
	end  
end

function sgs.ai_skill_pindian.MeowLieren(minusecard, self, requestor)
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(cards)
	if requestor:objectName() == self.player:objectName() then
		return cards[1]:getId()
	end
	return self:getMaxCard(self.player):getId()
end

sgs.ai_skill_use_func.MeowLierenCard = function(card, use, self,data)
	local max_card = self:getMaxCard()
	if not max_card then return end
	local point = max_card:getNumber()
	if use.to then 
		use.to:append(p) 
	end
end]]









--[[--献州（未完成）
local Meowxianzhou_skill = {}
Meowxianzhou_skill.name = "Meowxianzhou"
table.insert(sgs.ai_skills, Meowxianzhou_skill)
function SmartAI:shouldUseMXZ()
	if (self:hasCrossbowEffect() or self:getCardsNum("Crossbow") > 0) and self:getCardsNum("Slash") > 0 then
		self:sort(self.enemies, "defense")
		for _, enemy in ipairs(self.enemies) do
			local inAttackRange = self.player:distanceTo(enemy) == 1 or self.player:distanceTo(enemy) == 2 and self:getCardsNum("OffensiveHorse") > 0 and not self.player:getOffensiveHorse()
			if inAttackRange and sgs.isGoodTarget(enemy, self.enemies, self) then
				local slashs = self:getCards("Slash")
				local slash_count = 0
				for _, slash in ipairs(slashs) do
					if not self:slashProhibit(slash, enemy) and self:slashIsEffective(slash, enemy) then
						slash_count = slash_count + 1
					end
				end
				if slash_count >= enemy:getHp() then return false end
			end
		end
	end
	for _, enemy in ipairs(self.enemies) do
		if enemy:canSlash(self.player) and not self:slashProhibit(nil, self.player, enemy) then
			if enemy:hasWeapon("guding_blade") and self.player:getHandcardNum() == 1 and getCardsNum("Slash", enemy) >= 1 then
				return
			elseif self:hasCrossbowEffect(enemy) and getCardsNum("Slash", enemy) > 1 and self:getOverflow() <= 0 then
				return
			end
		end
	end
	for _, friend in ipairs(self.friends) do
		if self:isWeak(friend) then return true end
	end
	local keepNum = self.player:getHp()
	if self.player:hasSkill("kongcheng") then
		keepNum = 0
	end
	if self:getOverflow() > 0  then
		return true
	end
	if self.player:getHandcardNum() > keepNum  then
		return true
	end
end
Meowxianzhou_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("MeowxianzhouCard") or self.player:isNude() then return end
	if self.player:getMark("@handover") <= 0 then return end
	local mode = string.lower(global_room:getMode())
	if self:shouldUseMXZ() then
		return sgs.Card_Parse("@MeowxianzhouCard=.")
	end
end
sgs.ai_skill_use_func["#Meowxianzhou"] = function(card, use, self)
	if self:isWeak() then
		for _, friend in ipairs(self.friends_noself) do
			if friend:hasSkills(sgs.need_equip_skill) then
				use.card = card
				if use.to then use.to:append(friend) end
				return
			end
		end
		for _, friend in ipairs(self.friends_noself) do
			if not hasManjuanEffect(friend) then
				use.card = card
				if use.to then use.to:append(friend) end
				return
			end
		end
		self:sort(self.friends)
		for _, target in sgs.qlist(self.room:getOtherPlayers(self.player)) do
			local canUse = true
			for _, friend in ipairs(self.friends) do
				if target:inMyAttackRange(friend) and self:damageIsEffective(friend, nil, target)
					and not self:getDamagedEffects(friend, target) and not self:needToLoseHp(friend, target) then
					canUse = false
					break
				end
			end
			if canUse then
				use.card = card
				if use.to then use.to:append(target) end
				return
			end
		end
	end
	if not self.player:isWounded() then
		local killer
		self:sort(self.friends_noself)
		for _, target in sgs.qlist(self.room:getOtherPlayers(self.player)) do
			local canUse = false
			for _, friend in ipairs(self.friends_noself) do
				if friend:inMyAttackRange(target) and self:damageIsEffective(target, nil, friend)
					and not self:needToLoseHp(target, friend) and self:isWeak(target) then
					canUse = true
					killer = friend
					break
				end
			end
			if canUse then
				use.card = card
				if use.to then use.to:append(killer) end
				return
			end
		end
	end
	if #self.friends_noself == 0 then return end
	local n = self.player:getEquips():length()+self.player:getHandcardNum()
	if n > 2 or n > #self.enemies and sgs.turncount > 2 then
		local function cmp_AttackRange(a, b)
			local ar_a = a:getAttackRange()
			local ar_b = b:getAttackRange()
			if ar_a == ar_b then
				return sgs.getDefense(a) > sgs.getDefense(b)
			else
				return ar_a > ar_b
			end
		end
		table.sort(self.friends_noself, cmp_AttackRange)
		use.card = card
		if use.to then use.to:append(self.friends_noself[1]) end
	end
end
sgs.ai_skill_choice.Meowxianzhou=function(self, choices, data)
	local current = self.room:getCurrent()
	if self:isWeak(current) and self:isFriend(current) then 
		return "recover"
	else
		return "damage"
	end
end
sgs.ai_use_priority.MeowxianzhouCard = 4.9
sgs.ai_card_intention.MeowxianzhouCard = function(self, card, from, tos)
	if not from:isWounded() then sgs.updateIntentions(from, tos, -10) end
end
sgs.ai_skill_use["@@Meowxianzhou"] = function(self, prompt)
	--[[self:updatePlayers()
	local num = self.player:getMark("Meowxianzhou_count")
	--[[self:sort(self.enemies, "hp")
	for _, enemy in ipairs(self.enemies) do
		if self.player:inMyAttackRange(enemy) and self:damageIsEffective(enemy, nil, self.player) then
			table.insert(targets, enemy:objectName())
			if #targets == num then break end
		end
	end
	local room = self.room
	local Other = room:getOtherPlayers(self.player)
	local Enemy = sgs.SPlayerList()
	local player = self.player
	for _, p in sgs.qlist(Other) do
		if not self:isFriend(p) then
			Enemy:append(p)
			if Enemy:length() >= num then break end
		end
	end
	local targets=sgs.QList2Table(Enemy)
	local parseMeowxianzhouCard = function()
		if #targets == 0 then return "." end
		local s = table.concat(targets, "+")
		return "@MeowxianzhouCard=.->" .. s
	end
	return parseMeowxianzhouCard()
	self:updatePlayers()
	for _, p in pairs(sgs.QList2Table(self.room:getOtherPlayers(self.player))) do
		if p then
			return "@meowxianzhou=.->sgs1"
		end
	end
end]]
