function SmartAI:card_for_qiaobian(who,flag,to_friends,to_enemies)
	flag = flag or "ej"
	to_friends = to_friends or self.friends
	to_enemies = to_enemies or self.enemies
	local card,target
	if self:isFriend(who)
	then
		if flag:match("j")
		then
			for _,judge in sgs.qlist(who:getJudgingArea())do
				if not judge:isKindOf("YanxiaoCard")
				then
					for _,enemy in ipairs(to_enemies)do
						if not enemy:containsTrick(judge:objectName())
						and not enemy:containsTrick("YanxiaoCard")
						and not self.player:isProhibited(enemy,judge)
						and not (enemy:hasSkills("hongyan|ol<PERSON>yan") or judge:isKind<PERSON>f("Lightning"))
						then return enemy,judge end
					end
				end
			end
		end
		if flag:match("e")
		and who:hasEquip()
		then
			if self:hasSkills(sgs.lose_equip_skill,who)
			then
				for _,equip in sgs.qlist(who:getCards("e"))do
					if self:doDisCard(who,equip:getEffectiveId(),true)
					then
						if equip:isKindOf("DefensiveHorse")
						and not self:isWeak(who) then card = equip break
						elseif equip:isKindOf("Armor") and (not self:isWeak(who) or self:needToThrowArmor(who))
						then card = equip break else card = equip break end
					end
				end
				if card
				then
					local index = card:getRealCard():toEquipCard():location()
					if card:isKindOf("Armor") or card:isKindOf("DefensiveHorse")
					then self:sort(to_friends,"defense")
					else
						self:sort(to_friends,"handcard")
						to_friends = sgs.reverse(to_friends)
					end
					for _,friend in ipairs(to_friends)do
						if not friend:hasEquipArea(index) then continue end
						if not self:getSameEquip(card,friend) and friend:objectName()~=who:objectName()
						and self:hasSkills(sgs.need_equip_skill.."|"..sgs.lose_equip_skill,friend)
						then return friend,card end
					end
					for _,friend in ipairs(to_friends)do
						if not friend:hasEquipArea(index) then continue end
						if not self:getSameEquip(card,friend) and friend:objectName()~=who:objectName()
						then return friend,card end
					end
				end
			end
		end
	else
		if flag:match("j")
		and who:containsTrick("YanxiaoCard")
		then
			for _,judge in sgs.qlist(who:getJudgingArea())do
				if judge:isKindOf("YanxiaoCard")
				then
					for _,friend in ipairs(to_friends)do
						if not friend:containsTrick(judge:objectName())
						and not self.player:isProhibited(friend,judge)
						and friend:getJudgingArea():length()>0
						then return friend,judge end
					end
					for _,friend in ipairs(to_friends)do
						if not friend:containsTrick(judge:objectName())
						and not self.player:isProhibited(friend,judge)
						then return friend,judge end
					end
				end
			end
		end
		if flag:match("e")
		and who:hasEquip()
		then
			if self:hasSkills(sgs.lose_equip_skill,who) then return end
			local card_id = self:askForCardChosen(who,"e","snatch")
			if card_id>=0 and who:hasEquip(sgs.Sanguosha:getCard(card_id))
			then card = sgs.Sanguosha:getCard(card_id) end
			if card then
				local index = card:getRealCard():toEquipCard():location()
				if card:isKindOf("Armor") or card:isKindOf("DefensiveHorse")
				then self:sort(to_friends,"defense")
				else
					self:sort(to_friends,"handcard",true)
				end
				for _,friend in ipairs(to_friends)do
					if not friend:hasEquipArea(index) then continue end
					if not self:getSameEquip(card,friend) and friend:objectName()~=who:objectName()
					and self:hasSkills(sgs.lose_equip_skill.."|shensu|tenyearshensu" ,friend)
					then return friend,card end
				end
				for _,friend in ipairs(to_friends)do
					if not friend:hasEquipArea(index) then continue end
					if not self:getSameEquip(card,friend) and friend:objectName()~=who:objectName()
					then return friend,card end
				end
			end
		end
	end
end

sgs.ai_skill_discard.qiaobian = function(self,discard_num,min_num,optional,include_equip)
	local to_discard = {}
	local cards = self.player:getHandcards()
	cards = sgs.QList2Table(cards)
	self:sortByUseValue(cards,true)
	local stealer
	for _,ap in sgs.qlist(self.room:getOtherPlayers(self.player))do
		if ap:hasSkills("tuxi|nostuxi") and self:isEnemy(ap) then stealer = ap end
	end
	local card
	for i=1,#cards,1 do
		local isPeach = cards[i]:isKindOf("Peach")
		if isPeach then
			if stealer and self:isEnemy(stealer) and self.player:getHandcardNum()<=2 and self.player:getHp()>2
			and (not stealer:containsTrick("supply_shortage") or stealer:containsTrick("YanxiaoCard")) then
				card = cards[i]
				break
			end
			local to_discard_peach = true
			for _,fd in ipairs(self.friends)do
				if fd:getHp()<=2 and not fd:hasSkill("niepan") then
					to_discard_peach = false
				end
			end
			if to_discard_peach then
				card = cards[i]
				break
			end
		else
			card = cards[i]
			break
		end
	end
	if card==nil then return {} end
	table.insert(to_discard,card:getEffectiveId())
	current_phase = self.player:getMark("qiaobianPhase")
	if current_phase==sgs.Player_Judge and not self.player:isSkipped(sgs.Player_Judge) then
		if not self.player:containsTrick("YanxiaoCard") then
			if (self.player:containsTrick("lightning") and not self:hasWizard(self.friends) and self:hasWizard(self.enemies))
				or (self.player:containsTrick("lightning") and #self.friends>#self.enemies) then
				return to_discard
			elseif self.player:containsTrick("supply_shortage") then
				if self.player:getHp()>self.player:getHandcardNum() then return to_discard end
				local cardstr = sgs.ai_skill_use["@@nostuxi"](self,"@nostuxi")
				if cardstr:match("->") then
					local targetstr = cardstr:split("->")[2]
					local targets = targetstr:split("+")
					if #targets==2 then
						return to_discard
					end
				end
			elseif self.player:containsTrick("indulgence") then
				if self.player:getHandcardNum()>3 or self.player:getHandcardNum()>self.player:getHp()-1 then return to_discard end
				for _,friend in ipairs(self.friends_noself)do
					if not friend:containsTrick("YanxiaoCard") and (friend:containsTrick("indulgence") or friend:containsTrick("supply_shortage")) then
						return to_discard
					end
				end
			end
		end
	elseif current_phase==sgs.Player_Draw and not self.player:isSkipped(sgs.Player_Draw) and not self.player:hasSkills("tuxi|nostuxi") then
		local cardstr = sgs.ai_skill_use["@@nostuxi"](self,"@nostuxi")
		if cardstr:match("->") then
			local targetstr = cardstr:split("->")[2]
			local targets = targetstr:split("+")
			if #targets==2 then
				return to_discard
			end
		end
		return {}
	elseif current_phase==sgs.Player_Play and not self.player:isSkipped(sgs.Player_Play)
	then
		self:sortByKeepValue(cards)
		table.remove(to_discard)
		table.insert(to_discard,cards[1]:getEffectiveId())

		self:sort(self.enemies,"defense")
		self:sort(self.friends,"defense")
		self:sort(self.friends_noself,"defense")

		for _,friend in ipairs(self.friends)do
			if not friend:getCards("j"):isEmpty() and not friend:containsTrick("YanxiaoCard")
			and self:card_for_qiaobian(friend)
			then return to_discard end
		end

		for _,enemy in ipairs(self.enemies)do
			if not enemy:getCards("j"):isEmpty() and enemy:containsTrick("YanxiaoCard")
			and self:card_for_qiaobian(enemy)
			then return to_discard end
		end

		for _,friend in ipairs(self.friends_noself)do
			if not friend:getCards("e"):isEmpty()
			and self:hasSkills(sgs.lose_equip_skill,friend)
			and self:card_for_qiaobian(friend)
			then return to_discard end
		end

		local top_value = 0
		for _,hcard in ipairs(cards)do
			if not hcard:isKindOf("Jink") then
				if self:getUseValue(hcard)>top_value then top_value = self:getUseValue(hcard) end
			end
		end
		if top_value>=3.7 and #(self:getTurnUse())>0 then return {} end

		local targets = {}
		for _,enemy in ipairs(self.enemies)do
			if not self:hasSkills(sgs.lose_equip_skill,enemy)
			and self:card_for_qiaobian(enemy)
			then table.insert(targets,enemy) end
		end

		if #targets>0 then
			self:sort(targets,"defense")
			return to_discard
		end
	elseif current_phase==sgs.Player_Discard and not self.player:isSkipped(sgs.Player_Discard) then
		self:sortByKeepValue(cards)
		if self:needBear() then return {} end
		if self.player:getHandcardNum()>self:getOverflow(self.player,true) then
			return { cards[1]:getEffectiveId() }
		end
	end

	return {}
end

sgs.ai_skill_cardchosen.qiaobian = function(self,who,flags)
	if flags=="ej" then
		local to,c = self:card_for_qiaobian(who)
		return c
	end
end

sgs.ai_skill_playerchosen.qiaobian = function(self,targets)
	local who = self.room:getTag("QiaobianTarget"):toPlayer()
	if who then
		local to,c = self:card_for_qiaobian(who)
		return to
	end
end

sgs.ai_skill_use["@@qiaobian"] = function(self,prompt)
	local cards = self.player:getHandcards()
	cards = sgs.QList2Table(cards)
	self:sortByUseValue(cards,true)
	local card = cards[1]

	if prompt=="@qiaobian-2" then
		local cardstr = sgs.ai_skill_use["@@nostuxi"](self,"@nostuxi")
		if cardstr:match("->") then
			local targetstr = cardstr:split("->")[2]
			-- return "@QiaobianCard=."..card:getEffectiveId().."->"..targetstr
			return "@QiaobianCard=.->"..targetstr
		else
			return "."
		end
	end

	if prompt=="@qiaobian-3" then
		-- if self.player:getHandcardNum()-2>self.player:getHp() then return "." end

		self:sort(self.enemies,"defense")
		for _,friend in ipairs(self.friends)do
			if not friend:getCards("j"):isEmpty() and not friend:containsTrick("YanxiaoCard")
			and self:card_for_qiaobian(friend)
			then
				-- return "@QiaobianCard="..card:getEffectiveId().."->"..friend:objectName()
				return "@QiaobianCard=.->"..friend:objectName()
			end
		end

		for _,enemy in ipairs(self.enemies)do
			if not enemy:getCards("j"):isEmpty() and enemy:containsTrick("YanxiaoCard")
			and self:card_for_qiaobian(enemy)
			then
				-- return "@QiaobianCard="..card:getEffectiveId().."->"..friend:objectName()
				return "@QiaobianCard=.->"..enemy:objectName()
			end
		end

		for _,friend in ipairs(self.friends_noself)do
			if not friend:getCards("e"):isEmpty() and self:hasSkills(sgs.lose_equip_skill,friend)
			and self:card_for_qiaobian(friend)
			then
				return "@QiaobianCard=.->"..friend:objectName()
			end
		end

		local top_value = 0
		for _,hcard in ipairs(cards)do
			if not hcard:isKindOf("Jink") then
				if self:getUseValue(hcard)>top_value then top_value = self:getUseValue(hcard) end
			end
		end
		if top_value>=3.7 and #(self:getTurnUse())>0 then return "." end

		local targets = {}
		for _,enemy in ipairs(self.enemies)do
			if self:card_for_qiaobian(enemy)
			then
				table.insert(targets,enemy)
			end
		end

		if #targets>0 then
			self:sort(targets,"defense")
			-- return "@QiaobianCard="..card:getEffectiveId().."->"..targets[#targets]:objectName()
			return "@QiaobianCard=.->"..targets[#targets]:objectName()
		end
	end

	return "."
end

sgs.ai_card_intention.QiaobianCard = function(self,card,from,tos)
	if from:getMark("qiaobianPhase")==3 then return sgs.ai_card_intention.TuxiCard(self,card,from,tos) end
end

function sgs.ai_cardneed.qiaobian(to,card)
	return to:getCards("h"):length()<=2
end

sgs.ai_skill_invoke.tuntian = function(self,data)
	if #self.enemies==1 and self.room:alivePlayerCount()==2 and self:hasSkills("noswuyan|qianxun",self.enemies[1]) then
		if (self.player:hasSkill("zaoxian") and self.player:getMark("zaoxian")==0) or (self.player:hasSkill("olzaoxian") and self.player:getMark("olzaoxian")==0) then
			return false
		end
	end
	return true
end

sgs.ai_slash_prohibit.tuntian = function(self,from,to,card)
	if self:isFriend(to) then return false end
	if not to:hasSkill("zaoxian") then return false end
	if from:hasSkill("tieji") or self:canLiegong(to,from) then
		return false
	end
	local enemies = self:getEnemies(to)
	if #enemies==1 and self.room:alivePlayerCount()==2 and self:hasSkills("noswuyan|qianxun|weimu",enemies[1]) then return false end
	if getCardsNum("Jink",to,from)<1 or sgs.card_lack[to:objectName()]["Jink"]==1 or self:isWeak(to) then return false end
	if to:getHandcardNum()>=3 and to:hasSkill("zaoxian") then return true end
	return false
end

local jixi_skill = {}
jixi_skill.name = "jixi"
table.insert(sgs.ai_skills,jixi_skill)
jixi_skill.getTurnUseCard = function(self)
	if self.player:getHandcardNum()>=self.player:getHp()+2
	and self.player:getPile("field"):length()<=self.player:aliveCount()/2-1
	then return end
	for c,id in sgs.qlist(self.player:getPile("field"))do
		c = sgs.Card_Parse("snatch:jixi[no_suit:0]="..id)
		if c:isAvailable(self.player) then return c end
	end
end

sgs.ai_view_as.jixi = function(card,player,card_place)
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	if card_place==sgs.Player_PlaceSpecial and player:getPileName(card_id)=="field" then
		return ("snatch:jixi[%s:%s]=%d"):format(suit,number,card_id)
	end
end

sgs.ai_skill_cardask["@xiangle-discard"] = function(self,data)
	local target = data:toPlayer()
	if self:isFriend(target) and not self:findLeijiTarget(target,50,self.player) then return "." end
	local has_peach,has_analeptic,has_slash,has_jink
	for _,card in sgs.qlist(self.player:getHandcards())do
		if card:isKindOf("Peach") then has_peach = card
		elseif card:isKindOf("Analeptic") then has_analeptic = card
		elseif card:isKindOf("Slash") then has_slash = card
		elseif card:isKindOf("Jink") then has_jink = card
		end
	end

	if has_slash then return "$"..has_slash:getEffectiveId()
	elseif has_jink then return "$"..has_jink:getEffectiveId()
	elseif has_analeptic or has_peach then
		if getCardsNum("Jink",target,self.player)==0 and self.player:getMark("drank")>0 and self:getAllPeachNum(target)==0 then
			if has_analeptic then return "$"..has_analeptic:getEffectiveId()
			else return "$"..has_peach:getEffectiveId()
			end
		end
	else return "."
	end
end

function sgs.ai_slash_prohibit.xiangle(self,from,to)
	if self:isFriend(to,from) then return false end
	local slash_num,analeptic_num,jink_num
	if from:objectName()==self.player:objectName() then
		slash_num = self:getCardsNum("Slash")
		analeptic_num = self:getCardsNum("Analeptic")
		jink_num = self:getCardsNum("Jink")
	else
		slash_num = getCardsNum("Slash",from,self.player)
		analeptic_num = getCardsNum("Analpetic",from,self.player)
		jink_num = getCardsNum("Jink",from,self.player)
	end
	if self.player:getHandcardNum()==2 then
		if self.player:hasSkill("beifa") then self.player:setFlags("stack_overflow_xiangle") end
		local needkongcheng = self:needKongcheng()
		self.player:setFlags("-stack_overflow_xiangle")
		if needkongcheng then return slash_num+analeptic_num+jink_num<2 end
	end
	return slash_num+analeptic_num+math.max(jink_num-1,0)<2
end

sgs.ai_skill_invoke.fangquan = function(self,data)
	if #self.friends==1 then return false end

	-- First we'll judge whether it's worth skipping the Play Phase
	local cards = sgs.QList2Table(self.player:getHandcards())
	local shouldUse,range_fix = 0,0
	local hasCrossbow,slashTo = false,false
	for _,card in ipairs(cards)do
		if card:isKindOf("TrickCard") and self:getUseValue(card)>3.69 then
			local dummy_use = dummy()
			self:useTrickCard(card,dummy_use)
			if dummy_use.card then shouldUse = shouldUse+(card:isKindOf("ExNihilo") and 2 or 1) end
		end
		if card:isKindOf("Weapon") then
			local new_range = sgs.weapon_range[card:getClassName()] or 0
			local current_range = self.player:getAttackRange()
			range_fix = math.min(current_range-new_range,0)
		end
		if card:isKindOf("OffensiveHorse") and not self.player:getOffensiveHorse() then range_fix = range_fix-1 end
		if card:isKindOf("DefensiveHorse") or card:isKindOf("Armor") and not self:getSameEquip(card) and (self:isWeak() or self:getCardsNum("Jink")==0) then shouldUse = shouldUse+1 end
		if card:isKindOf("Crossbow") or self:hasCrossbowEffect() then hasCrossbow = true end
	end

	local slashs = self:getCards("Slash")
	for _,enemy in ipairs(self.enemies)do
		for _,slash in ipairs(slashs)do
			if hasCrossbow and self:getCardsNum("Slash")>1 and self:slashIsEffective(slash,enemy)
				and self.player:canSlash(enemy,slash,true,range_fix) then
				shouldUse = shouldUse+2
				hasCrossbow = false
				break
			elseif not slashTo and self:slashIsAvailable() and self:slashIsEffective(slash,enemy)
				and self.player:canSlash(enemy,slash,true,range_fix) and getCardsNum("Jink",enemy)<1 then
				shouldUse = shouldUse+1
				slashTo = true
			end
		end
	end
	if shouldUse>=2 then return end

	-- Then we need to find the card to be discarded
	local limit = self.player:getMaxCards()
	if self.player:isKongcheng() then return false end
	if self:getCardsNum("Peach")>=limit-2 and self.player:isWounded() then return false end

	local to_discard = nil

	local index = 0
	local all_peaches = 0
	for _,card in ipairs(cards)do
		if isCard("Peach",card,self.player) then
			all_peaches = all_peaches+1
		end
	end
	if all_peaches>=2 and self:getOverflow()<=0 then return false end
	self:sortByKeepValue(cards)
	cards = sgs.reverse(cards)

	for i = #cards,1,-1 do
		local card = cards[i]
		if not isCard("Peach",card,self.player) and not self.player:isJilei(card) then
			to_discard = card:getEffectiveId()
			break
		end
	end

	if to_discard then
		if sgs.playerRoles.rebel==0 then
			local lord = self.room:getLord()
			if lord and self:isFriend(lord) then
				return true
			end
		end

		local AssistTarget = self:AssistTarget()
		if AssistTarget and not self:willSkipPlayPhase(AssistTarget) then
			return true
		end

		self:sort(self.friends_noself,"handcard")
		self.friends_noself = sgs.reverse(self.friends_noself)
		for _,target in ipairs(self.friends_noself)do
			if not target:hasSkill("dawu") and target:hasSkills("yongsi|zhiheng|"..sgs.priority_skill.."|shensu")
				and (not self:willSkipPlayPhase(target) or target:hasSkill("shensu")) then
				return true
			end
		end

		for _,target in ipairs(self.friends_noself)do
			if target:hasSkill("dawu") then
				local use = true
				for _,p in ipairs(self.friends_noself)do
					if p:getMark("&dawu")>0 then use = false break end
				end
				if use then
					return true
				end
			else
				return true
			end
		end
	end
	return false
end

sgs.ai_skill_use["@@fangquan"] = function(self,prompt)
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(cards)
	if sgs.playerRoles.rebel==0 then
		local lord = self.room:getLord()
		if lord and self:isFriend(lord) and lord:objectName()~=self.player:objectName() then
			return "@FangquanCard="..cards[1]:getEffectiveId().."->"..lord:objectName()
		end
	end

	local AssistTarget = self:AssistTarget()
	if AssistTarget and not self:willSkipPlayPhase(AssistTarget) then
		return "@FangquanCard="..cards[1]:getEffectiveId().."->"..AssistTarget:objectName()
	end

	self:sort(self.friends_noself,"chaofeng")
	for _,target in ipairs(self.friends_noself)do
		if not target:hasSkill("dawu") and target:hasSkills("yongsi|zhiheng|"..sgs.priority_skill.."|shensu")
			and (not self:willSkipPlayPhase(target) or target:hasSkill("shensu")) then
			return "@FangquanCard="..cards[1]:getEffectiveId().."->"..target:objectName()
		end
	end

	for _,target in ipairs(self.friends_noself)do
		if target:hasSkill("dawu") then
			local use = true
			for _,p in ipairs(self.friends_noself)do
				if p:getMark("&dawu")>0 then use = false break end
			end
			if use then
				return "@FangquanCard="..cards[1]:getEffectiveId().."->"..target:objectName()
			end
		else
			return "@FangquanCard="..cards[1]:getEffectiveId().."->"..target:objectName()
		end
	end
	return "."
end

sgs.ai_card_intention.FangquanCard = -80

function SmartAI:isTiaoxinTarget(enemy)
	if not enemy then self.room:writeToConsole(debug.traceback()) return end
	if getCardsNum("Slash",enemy)<1 and self.player:getHp()>1 and not self:canHit(self.player,enemy)
		and not (enemy:hasWeapon("double_sword") and self.player:getGender()~=enemy:getGender())
		then return true end
	if sgs.card_lack[enemy:objectName()]["Slash"]==1
		or self:needLeiji(self.player,enemy)
		or self:needToLoseHp(self.player,enemy,dummyCard())
		then return true end
	if self:getOverflow() and self:getCardsNum("Jink")>1 then return true end
	return false
end

local tiaoxin_skill = {}
tiaoxin_skill.name = "tiaoxin"
table.insert(sgs.ai_skills,tiaoxin_skill)
tiaoxin_skill.getTurnUseCard = function(self)
	return sgs.Card_Parse("@TiaoxinCard=.")
end

sgs.ai_skill_use_func.TiaoxinCard = function(card,use,self)
	local distance = use.DefHorse and 1 or 0
	local targets = {}
	for _,enemy in ipairs(self.enemies)do
		if enemy:distanceTo(self.player,distance)<=enemy:getAttackRange() and self:doDisCard(enemy,"he") and self:isTiaoxinTarget(enemy) then
			table.insert(targets,enemy)
		end
	end

	if #targets==0 then return end

	sgs.ai_use_priority.TiaoxinCard = 8
	if not self.player:getArmor() and not self.player:isKongcheng() then
		for _,card in sgs.qlist(self.player:getCards("h"))do
			if card:isKindOf("Armor") and self:evaluateArmor(card)>3 then
				sgs.ai_use_priority.TiaoxinCard = 5.9
				break
			end
		end
	end

	self:sort(targets,"defenseSlash")
	use.to:append(targets[1])
	use.card = sgs.Card_Parse("@TiaoxinCard=.")
end

sgs.ai_skill_cardask["@tiaoxin-slash"] = function(self,data,pattern,target)
	if target then
		for _,slash in ipairs(self:getCards("Slash"))do
			if self:isFriend(target) and self:slashIsEffective(slash,target) then
				if self:needLeiji(target,self.player) then return slash:toString() end
				if self:needToLoseHp(target,self.player,nil,true) then return slash:toString() end
			end

			if not self:isFriend(target) and self:slashIsEffective(slash,target)
			and not self:needLeiji(target,self.player)
			then return slash:toString() end
		end
		for _,slash in ipairs(self:getCards("Slash"))do
			if not self:isFriend(target) then
				if not self:needLeiji(target,self.player) and not self:needToLoseHp(target,self.player,slash) then return slash:toString() end
				if not self:slashIsEffective(slash,target) then return slash:toString() end
			end
		end
	end
	return "."
end


sgs.ai_card_intention.TiaoxinCard = 80
sgs.ai_use_priority.TiaoxinCard = 4

sgs.ai_skill_choice.zhiji = function(self,choice)
	if self.player:getHp()<self.player:getMaxHp()-1 then return "recover" end
	return "draw"
end

sgs.ai_cardneed.jiang = function(to,card,self)
	return isCard("Duel",card,to) or (isCard("Slash",card,to) and card:isRed())
end

local zhiba_pindian_skill = {}
zhiba_pindian_skill.name = "zhiba_pindian"
table.insert(sgs.ai_skills,zhiba_pindian_skill)
zhiba_pindian_skill.getTurnUseCard = function(self)
	if  self:needBear() or self:getOverflow()<=0 or self.player:getKingdom()~="wu"
	then return end
	return sgs.Card_Parse("@ZhibaCard=.")
end

sgs.ai_use_priority.ZhibaCard = 0

sgs.ai_skill_use_func.ZhibaCard = function(card,use,self)
	local lords = {}
	for _,player in sgs.qlist(self.room:getOtherPlayers(self.player))do
		if player:hasLordSkill("zhiba") and self.player:canPindian(player) and not player:hasFlag("ZhibaInvoked")
			and not (self:isEnemy(player) and player:getMark("hunzi")>0) then
				table.insert(lords,player)
		end
	end
	if #lords==0 then return end
	self:sort(lords,"defense")
	for _,lord in ipairs(lords)do
		local zhiba_str
		local cards = self.player:getHandcards()

		local max_num,max_card = 0,nil
		local min_num,min_card = 14,nil
		for _,hcard in sgs.qlist(cards)do
			if hcard:getNumber()>max_num then
				max_num = hcard:getNumber()
				max_card = hcard
			end

			if hcard:getNumber()<=min_num then
				if hcard:getNumber()==min_num then
					if min_card and self:getKeepValue(hcard)>self:getKeepValue(min_card) then
						min_num = hcard:getNumber()
						min_card = hcard
					end
				else
					min_num = hcard:getNumber()
					min_card = hcard
				end
			end
		end

		local lord_max_num,lord_max_card = 0,nil
		local lord_min_num,lord_min_card = 14,nil
		local lord_cards = lord:getHandcards()
		local flag = string.format("%s_%s_%s","visible",global_room:getCurrent():objectName(),lord:objectName())
		for _,lcard in sgs.qlist(lord_cards)do
			if (lcard:hasFlag("visible") or lcard:hasFlag(flag)) and lcard:getNumber()>lord_max_num then
				lord_max_card = lcard
				lord_max_num = lcard:getNumber()
			end
			if lcard:getNumber()<lord_min_num then
				lord_min_num = lcard:getNumber()
				lord_min_card = lcard
			end
		end

		if self:isEnemy(lord) and max_num>10 and max_num>lord_max_num then
			if isCard("Jink",max_card,self.player) and self:getCardsNum("Jink")==1 then return end
			if isCard("Peach",max_card,self.player) or isCard("Analeptic",max_card,self.player) then return end
			self.zhiba_pindian_card = max_card
			zhiba_str = "@ZhibaCard=."
		end
		if self:isFriend(lord) and not lord:hasSkill("manjuan") and ((lord_max_num>0 and min_num<=lord_max_num) or min_num<7) then
			if isCard("Jink",min_card,self.player) and self:getCardsNum("Jink")==1 then return end
			self.zhiba_pindian_card = min_card
			zhiba_str = "@ZhibaCard=."
		end

		if zhiba_str then
			use.card = sgs.Card_Parse(zhiba_str)
			use.to:append(lord)
			return
		end
	end
end

sgs.ai_skill_choice.zhiba_pindian = function(self,choices)
	local who = self.room:getCurrent()
	local cards = self.player:getHandcards()
	local has_large_number,all_small_number = false,true
	for _,c in sgs.qlist(cards)do
		if c:getNumber()>11 then
			has_large_number = true
			break
		end
	end
	for _,c in sgs.qlist(cards)do
		if c:getNumber()>4 then
			all_small_number = false
			break
		end
	end
	if all_small_number or (self:isEnemy(who) and not has_large_number) then return "reject"
	else return "accept"
	end
end

sgs.ai_skill_choice.zhiba_pindian_obtain = function(self,choices)
	if self.player:isKongcheng() and self:needKongcheng() then return "reject" end
	return "obtainPindianCards"
end

function sgs.ai_skill_pindian.zhiba_pindian(minusecard,self,requestor,maxcard)
	local cards,maxcard = sgs.QList2Table(self.player:getHandcards())
	local function compare_func(a,b)
		return a:getNumber()>b:getNumber()
	end
	table.sort(cards,compare_func)
	for _,card in ipairs(cards)do
		if self:getUseValue(card)<6 then maxcard = card break end
	end
	return maxcard or cards[1]
end

sgs.ai_card_intention.ZhibaCard = 0
sgs.ai_choicemade_filter.pindian.zhiba_pindian = function(self,from,promptlist)
	local number = sgs.Sanguosha:getCard(tonumber(promptlist[4])):getNumber()
	local lord = self.room:findPlayerByObjectName(promptlist[5])
	if not lord then return end
	local lord_max_card = self:getMaxCard(lord)
	if lord_max_card and lord_max_card:getNumber()>=number then sgs.updateIntention(from,lord,-60)
	elseif lord_max_card and lord_max_card:getNumber()<number then sgs.updateIntention(from,lord,60)
	elseif number<6 then sgs.updateIntention(from,lord,-60)
	elseif number>8 then sgs.updateIntention(from,lord,60)
	end
end

sgs.ai_need_damaged.hunzi = function(self,attacker,player)
	if not player:hasSkill("chanyuan") and player:hasSkill("hunzi") and player:getMark("hunzi")==0 and self:getEnemyNumBySeat(self.room:getCurrent(),player,player,true)<player:getHp()
		and (player:getHp()>2 or player:getHp()==2 and (player:faceUp() or player:hasSkill("guixin") or player:hasSkill("toudu") and not player:isKongcheng())) then
		return true
	end
	return false
end

local zhijian_skill = {}
zhijian_skill.name = "zhijian"
table.insert(sgs.ai_skills,zhijian_skill)
zhijian_skill.getTurnUseCard = function(self)
	local equips = {}
	for _,card in sgs.qlist(self.player:getHandcards())do
		if card:getTypeId()==sgs.Card_TypeEquip then
			table.insert(equips,card)
		end
	end
	if #equips==0 then return end

	return sgs.Card_Parse("@ZhijianCard=.")
end

sgs.ai_skill_use_func.ZhijianCard = function(card,use,self)
	local equips = {}
	for _,card in sgs.qlist(self.player:getHandcards())do
		if card:isKindOf("Armor") or card:isKindOf("Weapon") then
			if not self:getSameEquip(card) then
			elseif card:isKindOf("GudingBlade") and self:getCardsNum("Slash")>0 then
				local HeavyDamage
				local slash = self:getCard("Slash")
				for _,enemy in ipairs(self.enemies)do
					if self.player:canSlash(enemy,slash,true) and not self:slashProhibit(slash,enemy) and
						self:slashIsEffective(slash,enemy) and not hasJueqingEffect(self.player,enemy) and enemy:isKongcheng() then
							HeavyDamage = true
							break
					end
				end
				if not HeavyDamage then table.insert(equips,card) end
			else
				table.insert(equips,card)
			end
		elseif card:getTypeId()==sgs.Card_TypeEquip then
			table.insert(equips,card)
		end
	end

	if #equips==0 then return end

	local select_equip,target
	for _,friend in ipairs(self.friends_noself)do
		for _,equip in ipairs(equips)do
			local index = equip:getRealCard():toEquipCard():location()
			if not friend:hasEquipArea(index) then continue end
			if not self:getSameEquip(equip,friend) and self:hasSkills(sgs.need_equip_skill.."|"..sgs.lose_equip_skill,friend) then
				target = friend
				select_equip = equip
				break
			end
		end
		if target then break end
		for _,equip in ipairs(equips)do
			local index = equip:getRealCard():toEquipCard():location()
			if not friend:hasEquipArea(index) then continue end
			if not self:getSameEquip(equip,friend) then
				target = friend
				select_equip = equip
				break
			end
		end
		if target then break end
	end

	if not target then return end
	use.to:append(target)
	local zhijian = sgs.Card_Parse("@ZhijianCard="..select_equip:getId())
	use.card = zhijian
end

sgs.ai_card_intention.ZhijianCard = -80
sgs.ai_use_priority.ZhijianCard = sgs.ai_use_priority.RendeCard+0.1  -- 刘备二张双将的话，优先直谏
sgs.ai_cardneed.zhijian = sgs.ai_cardneed.equip

sgs.ai_skill_use["@@guzheng"] = function(self,data)
	local card_ids = self.player:property("guzheng_allCards"):toString():split("+")
	local who = self.room:getCurrent()

	if self:isLihunTarget(self.player,#card_ids-1) then return "." end
	local invoke = (self:isFriend(who) and not (who:hasSkill("kongcheng") and who:isKongcheng()))
					or (#card_ids>=3 and not self.player:hasSkill("manjuan"))
					or (#card_ids==2 and not self:hasSkills(sgs.cardneed_skill,who) and not self.player:hasSkill("manjuan"))
					or (self:isEnemy(who) and who:hasSkill("kongcheng") and who:isKongcheng())
	if not invoke then return "." end

	local wulaotai = self.room:findPlayerBySkillName("buyi")
	local Need_buyi = wulaotai and who:getHp()==1 and self:isFriend(who,wulaotai)

	local cards,except_Equip,except_Key = {},{},{}
	for _,card_id in ipairs(card_ids)do
		local card = sgs.Sanguosha:getCard(card_id)
		if self.player:hasSkills("zhijian|mobilezhijianzhan") and not card:isKindOf("EquipCard") then
			table.insert(except_Equip,card)
		end
		if not card:isKindOf("Peach") and not card:isKindOf("Jink") and not card:isKindOf("Analeptic") and
			not card:isKindOf("Nullification") and not (card:isKindOf("EquipCard") and self.player:hasSkills("zhijian|mobilezhijianzhan")) then
			table.insert(except_Key,card)
		end
		table.insert(cards,card)
	end

	if self:isFriend(who) then

		if Need_buyi then
			local buyicard1,buyicard2
			self:sortByKeepValue(cards)
			for _,card in ipairs(cards)do
				if card:isKindOf("TrickCard") and not buyicard1 then
					buyicard1 = card:getEffectiveId()
				end
				if not card:isKindOf("BasicCard") and not buyicard2 then
					buyicard2 = card:getEffectiveId()
				end
				if buyicard1 then break end
			end
			if buyicard1 then
				return "@GuzhengCard="..buyicard1
			elseif buyicard2 then
				return "@GuzhengCard="..buyicard2
			end
		end

		local peach_num,peach,jink,analeptic,slash = 0,nil,nil,nil,nil
		for _,card in ipairs(cards)do
			if card:isKindOf("Peach") then peach = card:getEffectiveId() peach_num = peach_num+1 end
			if card:isKindOf("Jink") then jink = card:getEffectiveId() end
			if card:isKindOf("Analeptic") then analeptic = card:getEffectiveId() end
			if card:isKindOf("Slash") then slash = card:getEffectiveId() end
		end
		if peach then
			if peach_num>1
				or (self:getCardsNum("Peach")>=self.player:getMaxCards())
				or (who:getHp()<getBestHp(who) and who:getHp()<self.player:getHp()) then
					return "@GuzhengCard="..peach
			end
		end
		if self:isWeak(who) and (jink or analeptic) then
			if jink then
				return "@GuzhengCard="..jink
			elseif analeptic then
				return "@GuzhengCard="..analeptic
			end
		end

		for _,card in ipairs(cards)do
			if not card:isKindOf("EquipCard") then
				for _,askill in sgs.qlist(who:getVisibleSkillList(true))do
					local callback = sgs.ai_cardneed[askill:objectName()]
					if type(callback)=="function" and callback(who,card,self) then
						return "@GuzhengCard="..card:getEffectiveId()
					end
				end
			end
		end

		if jink or analeptic or slash then
			if jink then
				return "@GuzhengCard="..jink
			elseif analeptic then
				return "@GuzhengCard="..analeptic
			elseif slash then
				return "@GuzhengCard="..slash
			end
		end

		for _,card in ipairs(cards)do
			if not card:isKindOf("EquipCard") and not card:isKindOf("Peach") then
				return "@GuzhengCard="..card:getEffectiveId()
			end
		end

	else

		if Need_buyi then
			for _,card in ipairs(cards)do
				if card:isKindOf("Slash") then
					return "@GuzhengCard="..card:getEffectiveId()
				end
			end
		end

		for _,card in ipairs(cards)do
			if card:isKindOf("EquipCard") and self.player:hasSkills("zhijian|mobilezhijianzhan") then
				local Cant_Zhijian = true
				for _,friend in ipairs(self.friends)do
					if not self:getSameEquip(card,friend) then
						Cant_Zhijian = false
					end
				end
				if Cant_Zhijian then
					return "@GuzhengCard="..card:getEffectiveId()
				end
			end
		end

		local new_cards = (#except_Key>0 and except_Key) or (#except_Equip>0 and except_Equip) or cards

		self:sortByKeepValue(new_cards)
		local valueless,slash
		for _,card in ipairs (new_cards)do
			if card:isKindOf("Lightning") and not self:hasSkills(sgs.wizard_harm_skill,who) then
				return "@GuzhengCard="..card:getEffectiveId()
			end

			if card:isKindOf("Slash") then slash = card:getEffectiveId() end

			if not valueless and not card:isKindOf("Peach") then
				for _,askill in sgs.qlist(who:getVisibleSkillList(true))do
					local callback = sgs.ai_cardneed[askill:objectName()]
					if (type(callback)=="function" and not callback(who,card,self)) or not callback then
						valueless = card:getEffectiveId()
						break
					end
				end
			end
		end

		if slash or valueless then
			if slash then
				return "@GuzhengCard="..slash
			elseif valueless then
				return "@GuzhengCard="..valueless
			end
		end

		return "@GuzhengCard="..new_cards[1]:getEffectiveId()
	end
end

sgs.ai_skill_cardask["@beige"] = function(self,data)
	local damage = data:toDamage()
	if not self:isFriend(damage.to) or self:isFriend(damage.from) then return "." end
	local to_discard = self:askForDiscard("beige",1,1,false,true)
	if #to_discard>0 then return "$"..to_discard[1] else return "." end
end

function sgs.ai_cardneed.beige(to,card)
	return to:getCardCount()<=2
end

function sgs.ai_slash_prohibit.duanchang(self,from,to)
	if hasJueqingEffect(from,to) or (from:hasSkill("nosqianxi") and from:distanceTo(to)==1) then return false end
	if from:hasFlag("NosJiefanUsed") then return false end
	if to:getHp()>1 or #(self:getEnemies(from))==1 then return false end
	if from:getMaxHp()==3 and from:getArmor() and from:getDefensiveHorse() then return false end
	if from:getMaxHp()<=3 or (from:isLord() and self:isWeak(from)) then return true end
	if from:getMaxHp()<=3 or (self.room:getLord() and from:getRole()=="renegade") then return true end
	return false
end



sgs.ai_skill_invoke.huashen = function(self)
	return self.player:getHp()>0
end

sgs.ai_can_damagehp.xinsheng = function(self,from,card,to)
	return not self:isWeak()
	and self:canLoseHp(from,card,to)
end

function string:skillDefense()
	local function gdt(di)
		di = di:split(",")
		local m = 0
		for _,x in ipairs(di)do
			if x~="" then m = m+x end
		end
		return m/#di
	end
	local n = 0
	local dt = io.open("lua/ai/data/drawData", "r")
	if dt
	then
		for _,st in ipairs(dt:read("*all"):split("\n"))do
			if st:match(":") and st:match(self)
			then
				st = st:split(":")
				n = n+gdt(st[2])
			end
		end
		dt:close()
	end
	dt = io.open("lua/ai/data/damageData", "r")
	if dt
	then
		for _,st in ipairs(dt:read("*all"):split("\n"))do
			if st:match(":") and st:match(self)
			then
				st = st:split(":")
				n = n+gdt(st[2])
			end
		end
		dt:close()
	end
	dt = io.open("lua/ai/data/throwData", "r")
	if dt
	then
		for _,st in ipairs(dt:read("*all"):split("\n"))do
			if st:match(":") and st:match(self)
			then
				st = st:split(":")
				n = n+gdt(st[2])
			end
		end
		dt:close()
	end
	dt = io.open("lua/ai/data/convertData", "r")
	if dt
	then
		for _,st in ipairs(dt:read("*all"):split("\n"))do
			if st:match(":") and st:match(self)
			then
				st = st:split(":")
				n = n+gdt(st[2])
			end
		end
		dt:close()
	end
	return n
end

function sgs.ai_skill_choice.huashen(self,choices,data,xiaode_choice)
	local str = choices
	choices = str:split("+")
	local function compare_func(a,b)
		return a:skillDefense()>b:skillDefense()
	end
	table.sort(choices,compare_func)
	if not xiaode_choice and self.player:getHp()<1 and str:matchOne("nosbuqu") then return "nosbuqu" end
	if xiaode_choice and xiaode_choice>0
	or self.player:getPhase()==sgs.Player_RoundStart
	then
		if not xiaode_choice and self.player:getHp()<1 and str:matchOne("nosbuqu") then return "nosbuqu" end
		if self.player:getHandcardNum()>=self.player:getHp() and self.player:getHandcardNum()<10 and not self:isWeak()
		or self.player:isSkipped(sgs.Player_Play)
		then
			if str:matchOne("keji") then return "keji" end
		end
		if self.player:getHandcardNum()>4
		then
			for vs,askill in ipairs(choices)do
				vs = sgs.Sanguosha:getViewAsSkill(askill)
				if vs and not self.player:hasSkill(vs)
				and vs:isEnabledAtPlay(self.player) then return askill end
			end
			if self:findFriendsByType(sgs.Friend_Draw)
			then
				for _,askill in ipairs(("nosrende|rende|lirang|longluo"):split("|"))do
					if str:matchOne(askill) then return askill end
				end
			end
		end
		if self.player:getLostHp()>=2
		then
			if str:matchOne("qingnang") then return "qingnang" end
			if str:matchOne("jieyin") and self:findFriendsByType(sgs.Friend_MaleWounded) then return "jieyin" end
			if str:matchOne("nosrende") and self:findFriendsByType(sgs.Friend_Draw) then return "nosrende" end
			if str:matchOne("rende") and self:findFriendsByType(sgs.Friend_Draw) then return "rende" end
			for sk,ac in ipairs(choices)do
				sk = sgs.Sanguosha:getSkill(ac)
				if sk and string.find(sk:getDescription(),"已损失")-- or string.find(sk:getDescription(),"至体力上限")
				and not self.player:hasSkill(sk)
				then return ac end
			end
			if str:matchOne("miji") and self:findFriendsByType(sgs.Friend_Draw) then return "miji" end
		end
		if self.player:getHandcardNum()<2
		then
			if str:matchOne("haoshi") then return "haoshi" end
		end
		if self.player:isWounded()
		then
			if str:matchOne("qingnang") then return "qingnang" end
			if str:matchOne("jieyin") and self:findFriendsByType(sgs.Friend_MaleWounded) then return "jieyin" end
			if str:matchOne("nosrende") and self:findFriendsByType(sgs.Friend_Draw) then return "nosrende" end
			if str:matchOne("rende") and self:findFriendsByType(sgs.Friend_Draw) then return "rende" end
			for sk,ac in ipairs(choices)do
				sk = sgs.Sanguosha:getSkill(ac)
				if sk and string.find(sk:getDescription(),"已损失")-- or string.find(sk:getDescription(),"至体力上限")
				and not self.player:hasSkill(sk)
				then return ac end
			end
			if self.player:getHp()<2 and self.player:getHandcardNum()==1 and self:getCardsNum("Peach")<1 then
				if str:matchOne("shenzhi") then return "shenzhi" end
			end
		end
		if self.player:getEquips():length()>1
		then
			for sk,ac in ipairs(choices)do
				sk = sgs.Sanguosha:getSkill(ac)
				if sk and string.find(sk:getDescription(),"装备区")
				and not self.player:hasSkill(sk)
				then
					sk = sgs.Sanguosha:getTriggerSkill(ac)
					if sk and sk:hasEvent(sgs.CardsMoveOneTime)
					then return ac end
				end
			end
		end
		if self.player:getWeapon()
		then
			for sk,ac in ipairs(choices)do
				sk = sgs.Sanguosha:getSkill(ac)
				if sk and not self.player:hasSkill(sk)
				and string.find(sk:getDescription(),"武器牌")
				then return ac end
			end
		end
		for sk,ac in ipairs(choices)do
			sk = sgs.Sanguosha:getSkill(ac)
			if sk and not self.player:hasSkill(sk)
			and (string.find(sk:getDescription(),"准备阶段") or string.find(sk:getDescription(),"摸牌阶段"))
			then return ac end
		end
		if self:findFriendsByType(sgs.Friend_Draw)
		then
			for sk,ac in ipairs(choices)do
				sk = sgs.Sanguosha:getSkill(ac)
				if sk and string.find(sk:getDescription(),"出牌阶段")
				and not self.player:hasSkill(sk)
				then return ac end
			end
		end
		for vs,askill in ipairs(choices)do
			vs = sgs.Sanguosha:getViewAsSkill(askill)
			if vs and not self.player:hasSkill(vs)
			and vs:isEnabledAtPlay(self.player) then return askill end
		end
		for sk,ac in ipairs(choices)do
			sk = sgs.Sanguosha:getSkill(ac)
			if sk and string.find(sk:getDescription(),"阶段")
			and not self.player:hasSkill(sk)
			then return ac end
		end
	else
		if self.player:getHp()==1
		then
			for sk,ac in ipairs(choices)do
				sk = sgs.Sanguosha:getSkill(ac)
				if sk and (string.find(sk:getDescription(),"濒死") or string.find(sk:getDescription(),"死亡"))
				and not self.player:hasSkill(sk)
				then return ac end
			end
		end
		if self:getAllPeachNum()>0 or self.player:getHp()>1 or not self:isWeak()
		then
			for sk,ac in ipairs(choices)do
				sk = sgs.Sanguosha:getSkill(ac)
				if sk and string.find(sk:getDescription(),"受到")
				and not self.player:hasSkill(sk)
				then return ac end
			end
		end
		if self.room:alivePlayerCount()>2
		then
			for sk,ac in ipairs(choices)do
				sk = sgs.Sanguosha:getSkill(ac)
				if sk and string.find(sk:getDescription(),"成为")
				and not self.player:hasSkill(sk)
				then return ac end
			end
		end
		if self.player:isKongcheng() and str:matchOne("kongcheng")
		then return "kongcheng" end
		for sk,ac in ipairs(choices)do
			sk = sgs.Sanguosha:getSkill(ac)
			if sk and string.find(sk:getDescription(),"视为装备")
			and not self.player:hasSkill(sk)
			then return ac end
		end
		if not self.player:faceUp() then
			for sk,ac in ipairs(choices)do
				sk = sgs.Sanguosha:getSkill(ac)
				if sk and string.find(sk:getDescription(),"翻面")
				and not self.player:hasSkill(sk)
				then return ac end
			end
		end
		if self.player:getHandcardNum()>self.player:getHp() and self.player:getCards("e"):length()>0
		and str:matchOne("yanzheng") then return "yanzheng" end
		if self.player:getEquips():length()>1
		then
			for sk,ac in ipairs(choices)do
				sk = sgs.Sanguosha:getSkill(ac)
				if sk and string.find(sk:getDescription(),"装备区")
				and not self.player:hasSkill(sk)
				then
					sk = sgs.Sanguosha:getTriggerSkill(ac)
					if sk and sk:hasEvent(sgs.CardsMoveOneTime)
					then return ac end
				end
			end
		end
		for sk,ac in ipairs(choices)do
			sk = sgs.Sanguosha:getSkill(ac)
			if sk and string.find(sk:getDescription(),"防止")
			and not self.player:hasSkill(sk)
			then return ac end
		end
		for sk,ac in ipairs(choices)do
			sk = sgs.Sanguosha:getSkill(ac)
			if sk and string.find(sk:getDescription(),"对你无效")
			and not self.player:hasSkill(sk)
			then return ac end
		end
		for sk,ac in ipairs(choices)do
			sk = sgs.Sanguosha:getSkill(ac)
			if sk and string.find(sk:getDescription(),"你可以将")
			and not self.player:hasSkill(sk)
			then return ac end
		end
		for sk,ac in ipairs(choices)do
			sk = sgs.Sanguosha:getSkill(ac)
			if sk and string.find(sk:getDescription(),"受到")
			and not self.player:hasSkill(sk)
			then return ac end
		end
		for sk,ac in ipairs(choices)do
			sk = sgs.Sanguosha:getSkill(ac)
			if sk and string.find(sk:getDescription(),"当你使用")
			and not self.player:hasSkill(sk)
			then return ac end
		end
		for sk,ac in ipairs(choices)do
			sk = sgs.Sanguosha:getSkill(ac)
			if sk and string.find(sk:getDescription(),"当你成为")
			and not self.player:hasSkill(sk)
			then return ac end
		end
	end
	for i=1,#choices do
		if ("renjie|benghuai|shenjun|dongcha|yishe|shiyong|wumou|yaowu"):match(choices[i])
		then table.remove(choices,i) end
	end
	if not xiaode_choice and #choices>0 then
		return choices[math.random(1,#choices)]
	end
end

sgs.ai_suit_priority.jiang = function(self,card)
	return (card:isKindOf("Slash") or card:isKindOf("Duel")) and "diamond|heart|club|spade" or "club|spade|diamond|heart"
end

sgs.ai_cardneed.jiang = function(to,card,self)
	return isCard("Duel",card,to) or (isCard("Slash",card,to) and card:isRed())
end



local longhun_skill={}
longhun_skill.name="longhun"
table.insert(sgs.ai_skills,longhun_skill)
longhun_skill.getTurnUseCard = function(self)
	if self.player:getHp()>1 then return end
	local cards = self:addHandPile("he")
	self:sortByUseValue(cards,true)
	for _,card in sgs.list(cards)do
		if card:getSuit()==sgs.Card_Diamond and self:slashIsAvailable() then
			return sgs.Card_Parse(("fire_slash:longhun[%s:%s]=%d"):format(card:getSuitString(),card:getNumberString(),card:getId()))
		end
	end
end

sgs.ai_view_as.longhun = function(card,player,card_place)
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	if player:getHp()>1 or card_place==sgs.Player_PlaceSpecial then return end
	if card:getSuit()==sgs.Card_Diamond then
		return ("fire_slash:longhun[%s:%s]=%d"):format(suit,number,card_id)
	elseif card:getSuit()==sgs.Card_Club then
		return ("jink:longhun[%s:%s]=%d"):format(suit,number,card_id)
	elseif card:getSuit()==sgs.Card_Heart then
		return ("peach:longhun[%s:%s]=%d"):format(suit,number,card_id)
	elseif card:getSuit()==sgs.Card_Spade then
		return ("nullification:longhun[%s:%s]=%d"):format(suit,number,card_id)
	end
end

sgs.longhun_suit_value = {
	heart = 6.7,
	spade = 5,
	club = 4.2,
	diamond = 3.9,
}

function sgs.ai_cardneed.longhun(to,card,self)
	if to:getCardCount()>3 then return false end
	if to:isNude() then return true end
	return card:getSuit()==sgs.Card_Heart or card:getSuit()==sgs.Card_Spade
end

sgs.ai_skill_invoke.lianpo = true

function SmartAI:needBear(player)
	player = player or self.player
    return player:hasSkills("renjie+baiyin") and not player:hasSkill("jilve") and player:getMark("&bear")<4
end

sgs.ai_use_revises.renjie = function(self,card)
	if self.player:hasSkill("baiyin")
	and not self.player:hasSkill("jilve") and self.player:getMark("&bear")<4
	then
		if (card:isKindOf("Peach") or card:isKindOf("Armor")) and self.player:getLostHp()>1
		or card:isKindOf("TrickCard") and (card:targetFixed() and not card:isDamageCard() or card:canRecast() or ("snatch|collateral"):match(card:objectName()))
		then return end
		return false
	end
end

sgs.ai_skill_invoke.jilve_jizhi = function(self,data)
	local n = self.player:getMark("&bear")
	local use = (n>2 or self:getOverflow()>0)
	local card = data:toCardResponse().m_card
	card = card or data:toCardUse().card
	return use or card:isKindOf("ExNihilo")
end

sgs.ai_skill_invoke.jilve_guicai = function(self,data)
	local n = self.player:getMark("&bear")
	local use = (n>2 or self:getOverflow()>0)
	local judge = data:toJudge()
	if not self:needRetrial(judge) then return false end
	return (use or judge.who==self.player or judge.reason=="lightning")
	and self:getRetrialCardId(sgs.QList2Table(self.player:getHandcards()),judge)~=-1
end

sgs.ai_skill_invoke.jilve_fangzhu = function(self,data)
	return sgs.ai_skill_playerchosen.fangzhu(self,self.room:getOtherPlayers(self.player))~=nil
	or sgs.ai_skill_playerchosen.mobilefangzhu(self,self.room:getOtherPlayers(self.player))~=nil
end

sgs.ai_skill_choice._jilve = function(self,choices)
	choices = choices:split("+")
	if table.contains(choices,"jilve_tenyearjizhi") then return "jilve_tenyearjizhi" end
	if table.contains(choices,"jilve_mobilefangzhu") then
		if sgs.ai_skill_playerchosen.mobilefangzhu(self,self.room:getOtherPlayers(self.player))~=nil then
			return "jilve_mobilefangzhu"
		end
		return "fangzhu"
	end
	return choices[2]
end

local jilve_skill = {}
jilve_skill.name = "jilve"
table.insert(sgs.ai_skills,jilve_skill)
jilve_skill.getTurnUseCard = function(self)
	if not(self.player:hasFlag("JilveWansha") or self.player:hasSkills("wansha|olwansha"))
	then
		for _,enemy in sgs.list(self.enemies)do
			if self.player:canSlash(enemy) and self:isWeak(enemy)
			and self:damageMinusHp(enemy,1)>0 and #self.enemies>1
			then
				sgs.ai_use_priority.JilveCard = 8
				sgs.ai_skill_choice.jilve = sgs.Sanguosha:getSkill("olwansha") and "jilve_olwansha" or "wansha"
				return sgs.Card_Parse("@JilveCard=.")
			end
		end
	end
	if not self.player:hasFlag("JilveZhiheng")
	then
		sgs.ai_skill_choice.jilve = "jilve_tenyearzhiheng"
		sgs.ai_use_priority.JilveCard = sgs.ai_use_priority.TenyearZhihengCard
		local card = sgs.Card_Parse("@TenyearZhihengCard=.")
		local dummy_use = dummy()
		self:useSkillCard(card,dummy_use)
		if dummy_use.card then return sgs.Card_Parse("@JilveCard=.") end
	end
end

sgs.ai_skill_use_func.JilveCard=function(card,use,self)
	use.card = card
end

sgs.ai_skill_use["@zhiheng"]=function(self,prompt)
	local card=sgs.Card_Parse("@ZhihengCard=.")
	local dummy_use = dummy()
	self:useSkillCard(card,dummy_use)
	if dummy_use.card then return dummy_use.card:toString() end
	return "."
end

sgs.ai_skill_use["@tenyearzhiheng"]=function(self,prompt)
	local card=sgs.Card_Parse("@TenyearZhihengCard=.")
	local dummy_use = dummy()
	self:useSkillCard(card,dummy_use)
	if dummy_use.card then return dummy_use.card:toString() end
	return "."
end

--新龙魂
local newlonghun_skill = {}
newlonghun_skill.name = "newlonghun"
table.insert(sgs.ai_skills,newlonghun_skill)
newlonghun_skill.getTurnUseCard = function(self,inclusive)
	local usable_cards = self:addHandPile()
	local equips = sgs.QList2Table(self.player:getCards("e"))
	for _,e in sgs.list(equips)do
		if e:isKindOf("DefensiveHorse") or e:isKindOf("OffensiveHorse") then
			table.insert(usable_cards,e)
		end
	end
	self:sortByUseValue(usable_cards,true)
	local two_diamond_cards = {}
	for _,c in sgs.list(usable_cards)do
		if c:getSuit()==sgs.Card_Diamond and #two_diamond_cards<2 and not c:isKindOf("Peach") and not (c:isKindOf("WoodenOx") and self.player:getPile("wooden_ox"):length()>0) then
			table.insert(two_diamond_cards,c:getEffectiveId())
		end
	end
	if #two_diamond_cards==2 and self:slashIsAvailable() and self:getOverflow()>1 then
		return sgs.Card_Parse(("fire_slash:newlonghun[%s:%s]=%d+%d"):format("to_be_decided",0,two_diamond_cards[1],two_diamond_cards[2]))
	end
	for _,c in sgs.list(usable_cards)do
		if c:getSuit()==sgs.Card_Diamond and self:slashIsAvailable() and not c:isKindOf("Peach") and not (c:isKindOf("Jink") and self:getCardsNum("Jink")<3) and not (c:isKindOf("WoodenOx") and self.player:getPile("wooden_ox"):length()>0) then
			return sgs.Card_Parse(("fire_slash:newlonghun[%s:%s]=%d"):format(c:getSuitString(),c:getNumberString(),c:getEffectiveId()))
		end
	end
	for _,c in sgs.list(usable_cards)do
		if c:getSuit()==sgs.Card_Heart and self.player:getMark("Global_PreventPeach")==0 and not c:isKindOf("Peach") then
			return sgs.Card_Parse(("peach:newlonghun[%s:%s]=%d"):format(c:getSuitString(),c:getNumberString(),c:getEffectiveId()))
		end
	end
end

sgs.ai_view_as.newlonghun = function(card,player,card_place,class_name)
	if card_place==sgs.Player_PlaceSpecial then return end
	local current = player:getRoom():getCurrent()
	local usable_cards = sgs.QList2Table(player:getCards("he"))
	for _,id in sgs.list(player:getHandPile())do
		table.insert(usable_cards,sgs.Sanguosha:getCard(id))
	end
	local two_club_cards = {}
	local two_heart_cards = {}
	for _,c in sgs.list(usable_cards)do
		if c:getSuit()==sgs.Card_Club and #two_club_cards<2 then
			table.insert(two_club_cards,c:getEffectiveId())
		elseif c:getSuit()==sgs.Card_Heart and #two_heart_cards<2 then
			table.insert(two_heart_cards,c:getEffectiveId())
		end
	end
	
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	
	if #two_club_cards==2 and current and not current:isNude() and current:getWeapon() and current:getWeapon():isKindOf("Crossbow") then
		return ("jink:newlonghun[%s:%s]=%d+%d"):format("to_be_decided",0,two_club_cards[1],two_club_cards[2])
	elseif card:getSuit()==sgs.Card_Club then
		return ("jink:newlonghun[%s:%s]=%d"):format(suit,number,card_id)
	end
	
	local dying = player:getRoom():getCurrentDyingPlayer()
	if #two_heart_cards==2 and dying and not dying:hasSkill("newjuejing") then
		return ("peach:newlonghun[%s:%s]=%d+%d"):format("to_be_decided",0,two_heart_cards[1],two_heart_cards[2])
	elseif card:getSuit()==sgs.Card_Heart and player:getMark("Global_PreventPeach")==0 then
		return ("peach:newlonghun[%s:%s]=%d"):format(suit,number,card_id)
	end
	
	if card:getSuit()==sgs.Card_Diamond and not (card:isKindOf("WoodenOx") and player:getPile("wooden_ox"):length()>0) then
		return ("fire_slash:newlonghun[%s:%s]=%d"):format(suit,number,card_id)
	elseif card:getSuit()==sgs.Card_Spade then
		return ("nullification:newlonghun[%s:%s]=%d"):format(suit,number,card_id)
	end
end

sgs.newlonghun_suit_value = sgs.longhun_suit_value

function sgs.ai_cardneed.newlonghun(to,card,self)
	if to:getCardCount()>3 then return false end
	if to:isNude() then return true end
	return card:getSuit()==sgs.Card_Heart or card:getSuit()==sgs.Card_Spade
end

sgs.ai_need_damaged.newlonghun = function(self,attacker,player)
	if player:getHp()>1 and player:hasSkill("newjuejing") then return true end
end

