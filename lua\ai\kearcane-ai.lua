
--金克丝
sgs.ai_skill_playerchosen.kejinxmark = function(self, targets)
	targets = sgs.QList2Table(targets)
	local theweak = sgs.SPlayerList()
	local theweaktwo = sgs.SPlayerList()
	for _, p in ipairs(targets) do
		if self:isEnemy(p) then
			theweak:append(p)
		end
	end
	for _,qq in sgs.qlist(theweak) do
		if theweaktwo:isEmpty() then
			theweaktwo:append(qq)
		else
			local inin = 1
			for _,pp in sgs.qlist(theweaktwo) do
				if (pp:getHp() < qq:getHp()) then
					inin = 0
				end
			end
			if (inin == 1) then
				theweaktwo:append(qq)
			end
		end
	end
	if theweaktwo:length() > 0 then
	    return theweaktwo:at(0)
	end
	return nil
end

sgs.ai_skill_playerchosen.kejintuo = function(self, targets)
	targets = sgs.QList2Table(targets)
	local theweak = sgs.SPlayerList()
	local theweaktwo = sgs.SPlayerList()
	for _, p in ipairs(targets) do
		if self:isFriend(p) then
			theweak:append(p)
		end
	end
	for _,qq in sgs.qlist(theweak) do
		if theweaktwo:isEmpty() then
			theweaktwo:append(qq)
		else
			local inin = 1
			for _,pp in sgs.qlist(theweaktwo) do
				if (pp:getHp() < qq:getHp()) then
					inin = 0
				end
			end
			if (inin == 1) then
				theweaktwo:append(qq)
			end
		end
	end
	if theweaktwo:length() > 0 then
	    return theweaktwo:at(0)
	end
	return nil
end

local kefanmao_skill = {}
kefanmao_skill.name = "kefanmao"
table.insert(sgs.ai_skills, kefanmao_skill)
kefanmao_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("kefanmaoCard") then return end
	return sgs.Card_Parse("#kefanmaoCard:.:")
end

sgs.ai_skill_use_func["#kefanmaoCard"] = function(card, use, self)
    if (self.player:getMark("@hexstone")>=2) and not self.player:hasUsed("#kefanmaoCard") then
        self:sort(self.friends)
		for _, friend in ipairs(self.friends) do
			if self:isFriend(friend) and (self.player:objectName() ~= friend:objectName() ) then
				use.card = card
				if use.to then use.to:append(friend) end
				return
			end
		end
	end
end

sgs.ai_use_value.kefanmaoCard = 8.5
sgs.ai_use_priority.kefanmaoCard = 9.5
sgs.ai_card_intention.kefanmaoCard = 80

sgs.ai_skill_invoke.kebengneng = function(self, data)
	return true
end

sgs.ai_skill_invoke.kechetan = function(self, data)
	return true
end

sgs.ai_skill_invoke.kechetan = function(self, data)
	if self.player:hasFlag("wantusekechetan") then
		return true
	end
end

--vi

local keyuji_skill = {}
keyuji_skill.name = "keyuji"
table.insert(sgs.ai_skills, keyuji_skill)
keyuji_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("keyujiCard") then return end
	return sgs.Card_Parse("#keyujiCard:.:")
end

sgs.ai_skill_use_func["#keyujiCard"] = function(card, use, self)
    if not self.player:hasUsed("#keyujiCard") then
        self:sort(self.enemies)
	    self.enemies = sgs.reverse(self.enemies)
		local enys = sgs.SPlayerList()
		for _, enemy in ipairs(self.enemies) do
			if enys:isEmpty() then
				enys:append(enemy)
			else
				local yes = 1
				for _,p in sgs.qlist(enys) do
					if (enemy:getHp()+enemy:getHp()+enemy:getHandcardNum()) >= (p:getHp()+p:getHp()+p:getHandcardNum()) then
						yes = 0
					end
				end
				if (yes == 1) then
					enys:removeOne(enys:at(0))
					enys:append(enemy)
				end
			end
		end
		for _,enemy in sgs.qlist(enys) do
			if self:objectiveLevel(enemy) > 0 then
			    use.card = card
			    if use.to then use.to:append(enemy) end
		        return
			end
		end
	end
end

sgs.ai_use_value.keyujiCard = 8.5
sgs.ai_use_priority.keyujiCard = 9.5
sgs.ai_card_intention.keyujiCard = 80

sgs.ai_skill_invoke.kegonghuan = function(self, data)
	if self.player:hasFlag("wantusekegonghuan") then
		return true
	end
end

sgs.ai_skill_playerchosen.kechupan = function(self, targets)
	targets = sgs.QList2Table(targets)
	local theweak = sgs.SPlayerList()
	local theweaktwo = sgs.SPlayerList()
	for _, p in ipairs(targets) do
		if self:isEnemy(p) then
			theweak:append(p)
		end
	end
	for _,qq in sgs.qlist(theweak) do
		if theweaktwo:isEmpty() then
			theweaktwo:append(qq)
		else
			local inin = 1
			for _,pp in sgs.qlist(theweaktwo) do
				if (pp:getHp() < qq:getHp()) then
					inin = 0
				end
			end
			if (inin == 1) then
				theweaktwo:append(qq)
			end
		end
	end
	if theweaktwo:length() > 0 then
	    return theweaktwo:at(0)
	end
	return nil
end

sgs.ai_skill_discard.kesilang = function(self, discard_num, min_num, optional, include_equip) 
	local to_discard = {}
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	self:sortByKeepValue(cards)
	--if self.player:hasFlag("wantusepaomu") then
	    table.insert(to_discard, cards[1]:getEffectiveId())
		return to_discard
	--else
	   -- return self:askForDiscard("dummyreason", 999, 999, true, true)
	--end
end

sgs.ai_skill_playerchosen.kesilang = function(self, targets)
	targets = sgs.QList2Table(targets)
	local theweak = sgs.SPlayerList()
	local theweaktwo = sgs.SPlayerList()
	for _, p in ipairs(targets) do
		if self:isFriend(p) then
			theweak:append(p)
		end
	end
	for _,qq in sgs.qlist(theweak) do
		if theweaktwo:isEmpty() then
			theweaktwo:append(qq)
		else
			local inin = 1
			for _,pp in sgs.qlist(theweaktwo) do
				if (pp:getHp() < qq:getHp()) then
					inin = 0
				end
			end
			if (inin == 1) then
				theweaktwo:append(qq)
			end
		end
	end
	if theweaktwo:length() > 0 then
	    return theweaktwo:at(0)
	end
	return nil
end

sgs.ai_skill_invoke.kebloodangry = function(self, data)
	if self.player:hasFlag("wantusekebloodangry") then
		return true
	end
end

--garen
sgs.ai_skill_invoke.kezhengyi = function(self, data)
	if self.player:hasFlag("wantusekezhengyi") then
		return true
	end
end

local kehaojin_skill = {}
kehaojin_skill.name = "kehaojin"
table.insert(sgs.ai_skills, kehaojin_skill)
kehaojin_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("kehaojinCard") then return end
	--if (self.player:getMark("kechengzhengbing-Clear") >= 3) or (self.player:getKingdom() ~= "qun") or (self.player:isKongcheng()) then return end
	local card_id
	local cards = self.player:getHandcards()
	cards = sgs.QList2Table(cards)
	self:sortByKeepValue(cards)
	local to_throw = sgs.IntList()
	for _, acard in ipairs(cards) do
		to_throw:append(acard:getEffectiveId())
	end
	card_id = to_throw:at(0)--(to_throw:length()-1)
	if not card_id then
		return nil
	else
		return sgs.Card_Parse("#kehaojinCard:"..card_id..":")
	end
end

sgs.ai_skill_use_func["#kehaojinCard"] = function(card, use, self)
    if not self.player:hasUsed("#kehaojinCard") then 
        use.card = card
	    return
	end
end

function sgs.ai_cardneed.kehaojin(to, card, self)
	if self.player:hasUsed("kehaojinCard") then return false end
	return true
end

sgs.ai_use_value.kehaojinCard = 8.5
sgs.ai_use_priority.kehaojinCard = 9.5
sgs.ai_card_intention.kehaojinCard = -80

--lux

local keguangfu_skill = {}
keguangfu_skill.name = "keguangfu"
table.insert(sgs.ai_skills, keguangfu_skill)
keguangfu_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("keguangfuCard") then return end
	return sgs.Card_Parse("#keguangfuCard:.:")
end

sgs.ai_skill_use_func["#keguangfuCard"] = function(card, use, self)
    if not self.player:hasUsed("#keguangfuCard") then
        self:sort(self.enemies)
	    self.enemies = sgs.reverse(self.enemies)
		local enys = sgs.SPlayerList()
		for _, enemy in ipairs(self.enemies) do
			if self.player:canPindian(enemy,true) then
				if enys:isEmpty() then
					enys:append(enemy)
				else
					local yes = 1
					for _,p in sgs.qlist(enys) do
						if (enemy:getHp()+enemy:getHp()+enemy:getHandcardNum()) >= (p:getHp()+p:getHp()+p:getHandcardNum()) then
							yes = 0
						end
					end
					if (yes == 1) then
						enys:removeOne(enys:at(0))
						enys:append(enemy)
					end
				end
			end
		end
		for _,enemy in sgs.qlist(enys) do
			if self:objectiveLevel(enemy) > 0 then
			    use.card = card
			    if use.to then use.to:append(enemy) end
		        return
			end
		end
	end
end

sgs.ai_use_value.keguangfuCard = 8.5
sgs.ai_use_priority.keguangfuCard = 9.5
sgs.ai_card_intention.keguangfuCard = 80

sgs.ai_skill_playerchosen.kequguang = function(self, targets)
	targets = sgs.QList2Table(targets)
	local theweak = sgs.SPlayerList()
	local theweaktwo = sgs.SPlayerList()
	for _, p in ipairs(targets) do
		if self:isFriend(p) then
			theweak:append(p)
		end
	end
	for _,qq in sgs.qlist(theweak) do
		if theweaktwo:isEmpty() then
			theweaktwo:append(qq)
		else
			local inin = 1
			for _,pp in sgs.qlist(theweaktwo) do
				if (pp:getHp() < qq:getHp()) then
					inin = 0
				end
			end
			if (inin == 1) then
				theweaktwo:append(qq)
			end
		end
	end
	if theweaktwo:length() > 0 then
	    return theweaktwo:at(0)
	end
	return nil
end

local playmovesoldier_skill = {}
playmovesoldier_skill.name = "playmovesoldier"
table.insert(sgs.ai_skills, playmovesoldier_skill)
playmovesoldier_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("playmovesoldierCard") then return end
	return sgs.Card_Parse("#playmovesoldierCard:.:")
end

sgs.ai_skill_use_func["#playmovesoldierCard"] = function(card, use, self)
    if not self.player:hasUsed("#playmovesoldierCard") then
        self:sort(self.enemies)
	    self.enemies = sgs.reverse(self.enemies)
		local enys = sgs.SPlayerList()
		for _, enemy in ipairs(self.enemies) do
			if (enemy:getMark("@sandsoldier") == 0) then
				if enys:isEmpty() then
					enys:append(enemy)
				else
					local yes = 1
					for _,p in sgs.qlist(enys) do
						if (enemy:getHp()+enemy:getHp()+enemy:getHandcardNum()) >= (p:getHp()+p:getHp()+p:getHandcardNum()) then
							yes = 0
						end
					end
					if (yes == 1) then
						enys:removeOne(enys:at(0))
						enys:append(enemy)
					end
				end
			end
		end
		for _,enemy in sgs.qlist(enys) do
			if self:objectiveLevel(enemy) > 0 then
			    use.card = card
			    if use.to then use.to:append(enemy) end
		        return
			end
		end
	end
end

sgs.ai_use_value.playmovesoldierCard = 8.5
sgs.ai_use_priority.playmovesoldierCard = 9.5
sgs.ai_card_intention.playmovesoldierCard = 80


sgs.ai_skill_invoke.azirslash = function(self, data)
	return true
end

sgs.ai_skill_invoke.playmovesoldier = function(self, data)
	return true
end

sgs.ai_skill_playerchosen.playmovesoldier = function(self, targets)
	for _, p in ipairs(sgs.QList2Table(targets)) do
		if (self.player:objectName() == p:objectName()) and self:isWeak(p) then
			return p
		end
	end
	for _, p in ipairs(sgs.QList2Table(targets)) do
		if not self:isEnemy(p) then
			return p
		end
	end
end














